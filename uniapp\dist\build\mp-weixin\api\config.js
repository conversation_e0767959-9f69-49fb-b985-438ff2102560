"use strict";const E=require("../utils/env.js");exports.API_CONFIG={TIMEOUT:1e4,RETRY_COUNT:3,RETRY_DELAY:1e3},exports.ERROR_MESSAGES={NETWORK_ERROR:"网络连接失败，请检查网络设置",TIMEOUT_ERROR:"请求超时，请稍后重试",SERVER_ERROR:"服务器错误，请稍后重试",UNAUTHORIZED:"未授权访问，请重新登录",NOT_FOUND:"请求的资源不存在",BAD_REQUEST:"请求参数错误",UNKNOWN_ERROR:"未知错误，请稍后重试"},exports.HTTP_STATUS={BAD_REQUEST:400,UNAUTHORIZED:401,NOT_FOUND:404,INTERNAL_SERVER_ERROR:500},exports.STORAGE_KEYS={USER_INFO:"userInfo",USER_OPENID:"userOpenid",SELECTED_LIBRARY:"selectedLibrary",SELECTED_LEVEL:"selectedLevel",LEVEL_PROGRESS:"levelProgress"},exports.getWeixinApiUrl=R=>E.getApiUrl(R);
