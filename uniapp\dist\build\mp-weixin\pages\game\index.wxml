<view class="page-box data-v-dbde49c8"><view class="game-page-container data-v-dbde49c8"><view wx:if="{{a}}" class="level-detail-info card data-v-dbde49c8"><view class="level-header data-v-dbde49c8"><text class="level-name data-v-dbde49c8">{{b}}</text></view><view class="level-stats data-v-dbde49c8"><text class="stats-item data-v-dbde49c8">词组数量: {{c}}</text><text wx:if="{{d}}" class="stats-item data-v-dbde49c8">已完成</text><text wx:else class="stats-item data-v-dbde49c8">🎯 挑战中</text></view></view><view wx:elif="{{e}}" class="selected-library-info card data-v-dbde49c8"><text class="library-name data-v-dbde49c8">{{f}}</text></view><view wx:if="{{g}}" class="game-area card data-v-dbde49c8"><view class="game-info-bar data-v-dbde49c8"><text class="{{['game-timer', 'data-v-dbde49c8', i && 'time-warning']}}"> ⏱️ {{h}}</text><text wx:if="{{j}}" class="sync-status data-v-dbde49c8">同步中...</text><text wx:if="{{k}}" class="h5-mock-tip data-v-dbde49c8">H5演示</text><view class="{{['replay-btn', 'data-v-dbde49c8', m && 'replay-btn-disabled']}}" bindtap="{{n}}"><text class="replay-btn-text data-v-dbde49c8">{{l}}</text></view></view><view wx:if="{{o}}" class="loading-container data-v-dbde49c8"><view class="loading-spinner data-v-dbde49c8"></view><text class="loading-text data-v-dbde49c8">正在初始化游戏...</text></view><view wx:elif="{{p}}" class="loading-container data-v-dbde49c8"><view class="loading-spinner data-v-dbde49c8"></view><text class="loading-text data-v-dbde49c8">正在计算卡片位置...</text></view><view wx:elif="{{q}}" class="loading-container data-v-dbde49c8"><view class="loading-spinner data-v-dbde49c8"></view><text class="loading-text data-v-dbde49c8">准备渲染游戏...</text></view><view wx:elif="{{r}}" class="game-loading-container data-v-dbde49c8"><view class="game-loading-content data-v-dbde49c8"><view class="game-loading-spinner data-v-dbde49c8"><view class="spinner-ring data-v-dbde49c8"></view><view class="spinner-ring data-v-dbde49c8"></view><view class="spinner-ring data-v-dbde49c8"></view></view><text class="game-loading-title data-v-dbde49c8">{{s}}</text><text class="game-loading-subtitle data-v-dbde49c8">{{t}}</text><view class="loading-progress data-v-dbde49c8"><view class="progress-bar data-v-dbde49c8"><view class="progress-fill data-v-dbde49c8" style="{{'width:' + v}}"></view></view><text class="progress-text data-v-dbde49c8">{{w}}%</text></view></view></view><view wx:elif="{{x}}" class="error-container data-v-dbde49c8"><text class="error-text data-v-dbde49c8">游戏初始化失败</text><view class="retry-btn data-v-dbde49c8" bindtap="{{y}}"><text class="retry-btn-text data-v-dbde49c8">重试</text></view></view><view wx:elif="{{z}}" class="{{['game-board', 'data-v-dbde49c8', B && 'checking-match']}}"><view wx:for="{{A}}" wx:for-item="tile" wx:key="e" hidden="{{!tile.f}}" class="{{['board-tile', 'data-v-dbde49c8', tile.g && 'selected', tile.h && 'matched', tile.i && 'tile-english', tile.j && 'tile-chinese', tile.k && 'tile-short', tile.l && 'tile-medium', tile.m && 'tile-long']}}" style="{{'background-color:' + tile.n + ';' + ('position:' + 'absolute') + ';' + ('left:' + tile.o) + ';' + ('top:' + tile.p) + ';' + ('width:' + tile.q) + ';' + ('height:' + tile.r)}}" bindtap="{{tile.s}}"><text wx:if="{{tile.a}}" class="tile-word data-v-dbde49c8">{{tile.b}}</text><text wx:if="{{tile.c}}" class="tile-chinese-only data-v-dbde49c8">{{tile.d}}</text></view></view></view><view wx:if="{{C}}" class="modal-overlay data-v-dbde49c8"><view class="modal-content data-v-dbde49c8"><text class="modal-title data-v-dbde49c8">{{D}}</text><view wx:if="{{E}}" class="game-completion-info data-v-dbde49c8"><view class="stars-display data-v-dbde49c8"><text class="stars-label data-v-dbde49c8">获得星级</text><view class="stars-container data-v-dbde49c8"><text wx:for="{{F}}" wx:for-item="star" wx:key="a" class="{{['star-icon', 'data-v-dbde49c8', star.b && 'star-filled']}}"> ⭐ </text></view></view><view class="completion-time data-v-dbde49c8"><text class="time-label data-v-dbde49c8">完成时间</text><text class="time-value data-v-dbde49c8">{{G}}</text></view><view class="favorite-section data-v-dbde49c8"><button class="{{['favorite-btn', 'data-v-dbde49c8', J && 'favorited']}}" bindtap="{{K}}"><text class="favorite-icon data-v-dbde49c8">{{H}}</text><text class="favorite-text data-v-dbde49c8">{{I}}</text></button></view></view><view class="modal-buttons data-v-dbde49c8"><button wx:if="{{L}}" bindtap="{{N}}" class="modal-button primary data-v-dbde49c8">{{M}}</button><button bindtap="{{O}}" class="modal-button data-v-dbde49c8">再试一次</button><button bindtap="{{P}}" class="modal-button data-v-dbde49c8">返回首页</button></view></view></view></view><settings-modal wx:if="{{S}}" class="data-v-dbde49c8" bindclose="{{Q}}" bindsettingsChange="{{R}}" u-i="dbde49c8-0" bind:__l="__l" u-p="{{S}}"/></view>