<view class="test-audio-container data-v-e752d929"><view class="header data-v-e752d929"><text class="title data-v-e752d929">音频功能测试</text><text class="subtitle data-v-e752d929">测试新的 InnerAudioContext API</text></view><view class="status-card data-v-e752d929"><text class="status-title data-v-e752d929">播放状态</text><view class="status-info data-v-e752d929"><text class="status-item data-v-e752d929">正在播放: {{a}}</text><text class="status-item data-v-e752d929">已暂停: {{b}}</text><text class="status-item data-v-e752d929">音乐类型: {{c}}</text><text class="status-item data-v-e752d929">音频源: {{d}}</text></view></view><view class="control-section data-v-e752d929"><text class="section-title data-v-e752d929">🎵 背景音乐控制</text><view class="button-grid data-v-e752d929"><button class="control-btn primary data-v-e752d929" bindtap="{{e}}">播放主页音乐</button><button class="control-btn primary data-v-e752d929" bindtap="{{f}}">播放游戏音乐</button><button class="control-btn primary data-v-e752d929" bindtap="{{g}}">播放菜单音乐</button><button class="control-btn secondary data-v-e752d929" bindtap="{{h}}">暂停音乐</button><button class="control-btn secondary data-v-e752d929" bindtap="{{i}}">恢复音乐</button><button class="control-btn danger data-v-e752d929" bindtap="{{j}}">停止音乐</button></view></view><view class="control-section data-v-e752d929"><text class="section-title data-v-e752d929">🔊 音效测试</text><view class="button-grid data-v-e752d929"><button class="control-btn effect data-v-e752d929" bindtap="{{k}}">点击音效</button><button class="control-btn effect data-v-e752d929" bindtap="{{l}}">成功音效</button><button class="control-btn effect data-v-e752d929" bindtap="{{m}}">失败音效</button><button class="control-btn effect data-v-e752d929" bindtap="{{n}}">解锁音效</button><button class="control-btn effect data-v-e752d929" bindtap="{{o}}">完成音效</button></view></view><view class="control-section data-v-e752d929"><text class="section-title data-v-e752d929">📳 震动测试</text><view class="button-grid data-v-e752d929"><button class="control-btn vibrate data-v-e752d929" bindtap="{{p}}">短震动</button><button class="control-btn vibrate data-v-e752d929" bindtap="{{q}}">长震动</button></view></view><view class="control-section data-v-e752d929"><text class="section-title data-v-e752d929">⚙️ 设置控制</text><view class="settings-list data-v-e752d929"><view class="setting-item data-v-e752d929"><text class="setting-label data-v-e752d929">背景音乐</text><switch class="data-v-e752d929" checked="{{r}}" bindchange="{{s}}" color="#667eea"/></view><view class="setting-item data-v-e752d929"><text class="setting-label data-v-e752d929">音效</text><switch class="data-v-e752d929" checked="{{t}}" bindchange="{{v}}" color="#667eea"/></view><view class="setting-item data-v-e752d929"><text class="setting-label data-v-e752d929">震动</text><switch class="data-v-e752d929" checked="{{w}}" bindchange="{{x}}" color="#667eea"/></view></view></view><view class="control-section data-v-e752d929"><text class="section-title data-v-e752d929">🌐 全局配置测试</text><view class="config-info data-v-e752d929"><text class="config-item data-v-e752d929">主页音乐URL: {{y}}</text><text class="config-item data-v-e752d929">游戏音乐URL: {{z}}</text><text class="config-item data-v-e752d929">菜单音乐URL: {{A}}</text></view><view class="button-grid data-v-e752d929"><button class="control-btn config data-v-e752d929" bindtap="{{B}}">测试全局配置音乐</button><button class="control-btn config data-v-e752d929" bindtap="{{C}}">刷新配置</button></view></view><view class="back-btn-container data-v-e752d929"><button class="back-btn data-v-e752d929" bindtap="{{D}}"><text class="back-text data-v-e752d929">返回</text></button></view></view>