"use strict";var e=Object.defineProperty;const t=require("../common/vendor.js"),n=require("./env.js");require("../api/request.js");const r=require("../api/weixin.js");class o{static async getShareConfig(e){try{return await r.weixinApi.getShareConfig(e)}catch(t){return"development"===n.getCurrentEnvironment()&&console.error("获取分享配置失败:",t),{title:"趣护消消乐 - 挑战你的词汇量！",path:"/pages/index/index",imageUrl:"/static/share-logo.png",desc:"快来挑战趣护消消乐，提升你的词汇量！",summary:"快来挑战趣护消消乐，提升你的词汇量！"}}}static async handleShareAppMessage(e,t){try{const r="development"===n.getCurrentEnvironment();r&&console.log("处理小程序分享:",e);const o=await this.getShareConfig(t),a={title:o.title,path:o.path,imageUrl:o.imageUrl};if(o.query){const e=a.path.includes("?")?"&":"?";a.path+=e+o.query}return r&&console.log("分享数据:",a),(null==t?void 0:t.userId)&&"menu"===e.from&&setTimeout(async()=>{try{await this.claimShareReward({userId:t.userId,shareType:"app_message",page:t.page,levelId:t.levelId,timestamp:Date.now()})}catch(e){"development"===n.getCurrentEnvironment()&&console.error("获取分享奖励失败:",e)}},1e3),a}catch(r){return console.error("处理分享失败:",r),{title:"趣护消消乐",path:"/pages/index/index",imageUrl:"/static/share-logo.png"}}}static checkDailyShareReward(e){try{const r=(new Date).toDateString(),o=`daily_share_reward_${e}_${r}`,a=t.index.getStorageSync(o);return"development"===n.getCurrentEnvironment()&&console.log(`检查每日分享奖励状态 - 用户: ${e}, 日期: ${r}, 已分享: ${!!a}`),!!a}catch(r){return"development"===n.getCurrentEnvironment()&&console.error("检查每日分享奖励状态失败:",r),!1}}static markDailyShareReward(e){try{const r=(new Date).toDateString(),o=`daily_share_reward_${e}_${r}`;t.index.setStorageSync(o,!0),"development"===n.getCurrentEnvironment()&&console.log(`标记每日分享奖励完成 - 用户: ${e}, 日期: ${r}`)}catch(r){"development"===n.getCurrentEnvironment()&&console.error("标记每日分享奖励失败:",r)}}static async claimShareReward(e){try{const o="development"===n.getCurrentEnvironment();if(this.isClaimingReward)return void(o&&console.log("分享奖励正在处理中，跳过重复请求"));if(this.checkDailyShareReward(e.userId))return o&&console.log("今日已获取过分享奖励，跳过本次请求"),void t.index.showToast({title:"今日已获得分享奖励",icon:"none",duration:2e3});this.isClaimingReward=!0,o&&console.log("开始获取分享奖励:",e);const a=await r.weixinApi.getShareReward();a.success?(this.markDailyShareReward(e.userId),t.index.showToast({title:`获得${a.reward.description}！`,icon:"success",duration:3e3}),o&&console.log("分享奖励获取成功:",a.reward)):(o&&console.log("分享奖励获取失败:",a.message),(a.message.includes("今日")||a.message.includes("已领取"))&&this.markDailyShareReward(e.userId))}catch(o){"development"===n.getCurrentEnvironment()&&console.error("获取分享奖励失败:",o)}finally{this.isClaimingReward=!1,"development"===n.getCurrentEnvironment()&&console.log("分享奖励处理完成，重置状态")}}static async shareToWeixin(e){try{console.log("非App环境，无法使用uni.share"),t.index.showToast({title:"当前环境不支持分享",icon:"none"})}catch(n){console.error("App分享失败:",n),t.index.showToast({title:"分享失败",icon:"none"})}}static async shareWithSystem(e){try{const r=await this.getShareConfig(e),o={type:"text",summary:`${r.title}\n${r.desc||r.summary}`,href:r.path,success:()=>{"development"===n.getCurrentEnvironment()&&console.log("系统分享成功")},fail:e=>{console.error("系统分享失败:",e)}};t.index.shareWithSystem(o)}catch(r){console.error("系统分享失败:",r),t.index.showToast({title:"分享失败",icon:"none"})}}static showShareMenu(e){try{t.index.showShareMenu(e||{}),"development"===n.getCurrentEnvironment()&&console.log("显示分享菜单成功")}catch(r){console.error("显示分享菜单失败:",r)}}static hideShareMenu(){try{t.index.hideShareMenu({hideShareItems:[]}),"development"===n.getCurrentEnvironment()&&console.log("隐藏分享菜单成功")}catch(e){console.error("隐藏分享菜单失败:",e)}}static generateSharePath(e,t){let n=e.startsWith("/")?e:`/${e}`;if(t&&Object.keys(t).length>0){n+=`?${Object.entries(t).map(([e,t])=>`${encodeURIComponent(e)}=${encodeURIComponent(t)}`).join("&")}`}return n}static parseShareParams(e){const t={};if(e){const n=e.split("&");for(const e of n){const[n,r]=e.split("=");n&&r&&(t[decodeURIComponent(n)]=decodeURIComponent(r))}}return t}}var a;((t,n,r)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r})(o,"symbol"!=typeof(a="isClaimingReward")?a+"":a,!1);const s=o;exports.shareUtils=s;
