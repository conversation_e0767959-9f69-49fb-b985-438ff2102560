"use strict";const e=require("../common/vendor.js");require("../api/request.js");const n=require("../api/weixin.js");function r(){try{const e=n.weixinApi.getLocalUserInfo(),r=n.weixinApi.getOpenid();return!(!e||!r||"openid"===r)}catch(e){return console.error("检查登录状态失败:",e),!1}}exports.checkLoginAndRedirect=async function(n){const{showToast:o=!0,toastMessage:i="请先登录",redirectUrl:t="/pages/login/index"}=n||{};return!!r()||(o&&e.index.showToast({title:i,icon:"none",duration:2e3}),setTimeout(()=>{e.index.navigateTo({url:t})},1e3),!1)},exports.getCurrentUser=function(){try{return n.weixinApi.getLocalUserInfo()}catch(e){return console.error("获取当前用户信息失败:",e),null}},exports.isUserLoggedIn=r,exports.isWeixinCodeUsedError=function(e){const n=e instanceof Error?e.message:String(e);return n.includes("code已过期")||n.includes("code已经被使用")||n.includes("code been used")||n.includes("40163")};
