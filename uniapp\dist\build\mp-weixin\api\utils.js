"use strict";const n=require("../common/vendor.js");exports.appendQueryToUrl=function(n,o){const t=function(n){const o=[];for(const[t,e]of Object.entries(n))null!=e&&""!==e&&o.push(`${encodeURIComponent(t)}=${encodeURIComponent(String(e))}`);return o.join("&")}(o);if(!t)return n;const e=n.includes("?")?"&":"?";return`${n}${e}${t}`},exports.createLoadingState=function(){return n.reactive({isLoading:!1,error:null})},exports.showError=function(o,t=2e3){n.index.showToast({title:o,icon:"none",duration:t})},exports.showSuccess=function(o,t=1500){n.index.showToast({title:o,icon:"success",duration:t})},exports.withLoading=async function(o,t,e){const{showToast:r=!0,toastDuration:s=2e3,errorMessage:i}=e||{};try{o.isLoading=!0,o.error=null;return await t()}catch(c){const t=i||c.message||"操作失败";return o.error=t,r&&n.index.showToast({title:t,icon:"none",duration:s}),console.error("异步操作失败:",c),null}finally{o.isLoading=!1}};
