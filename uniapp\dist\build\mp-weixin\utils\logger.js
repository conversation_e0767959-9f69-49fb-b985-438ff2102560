"use strict";const o=require("./env.js");function e(){return"development"===o.getCurrentEnvironment()}const n=(...o)=>{e()&&console.log(...o)},r=(...o)=>{e()&&console.info(...o)},s=(...o)=>{e()&&console.warn(...o)},t=(...o)=>{e()?console.error(...o):console.error("An error occurred")},c=(...o)=>{e()&&console.debug("[DEBUG]",...o)},l=o=>{e()&&console.time(o)},g=o=>{e()&&console.timeEnd(o)},$=o=>{e()&&console.group(o)},u=()=>{e()&&console.groupEnd()},i=o=>{e()&&console.table(o)},a=(o,...n)=>{e()&&console.assert(o,...n)};const d={login:(p="登录",{log:(...o)=>n(`[${p}]`,...o),info:(...o)=>r(`[${p}]`,...o),warn:(...o)=>s(`[${p}]`,...o),error:(...o)=>t(`[${p}]`,...o),debug:(...o)=>c(`[${p}]`,...o),time:o=>l(`[${p}] ${o}`),timeEnd:o=>g(`[${p}] ${o}`),group:o=>$(o?`[${p}] ${o}`:`[${p}]`),groupEnd:()=>u(),table:o=>i(o),assert:(o,...e)=>a(o,`[${p}]`,...e)})};var p;exports.pageLogger=d;
