"use strict";const e=require("../../common/vendor.js"),a=require("../../api/weixin.js"),i=require("../../api/utils.js"),t=require("../../utils/audio.js"),o=e.defineComponent({__name:"index",setup(o){const l=e.ref(!0),n=e.ref(""),s=e.ref([]),r=e.ref(null),c=e.ref(null),u=e.ref(!1);e.computed(()=>s.value.filter(e=>"active"===e.status).length),e.computed(()=>s.value.filter(e=>e.isVip).length);const v=async()=>{try{l.value=!0,n.value="",console.log("�️ 开始加载标签列表...");const[i,t]=await Promise.all([a.weixinApi.getTags(),a.weixinApi.getDailyStatus()]);s.value=i,c.value=t,u.value=t.isVip,console.log("✅ 标签列表加载成功:",i.length,"个标签"),console.log("✅ VIP状态:",t.isVip?"VIP用户":"普通用户");try{const e=await a.weixinApi.getUserStarStats();r.value=e,console.log("✅ 用户统计信息加载成功:",e)}catch(e){console.warn("⚠️ 用户统计信息加载失败:",e)}}catch(i){console.error("❌ 数据加载失败:",i),n.value=i instanceof Error?i.message:"加载数据失败"}finally{l.value=!1}},d=()=>{e.index.showModal({title:"VIP专享内容",content:"该标签为VIP专享内容，开通VIP会员即可畅玩所有标签关卡！",confirmText:"开通VIP",cancelText:"取消",success:a=>{a.confirm&&e.index.navigateTo({url:"/pages/member-center/index"})}})},g=()=>{t.audioManager.playSoundEffect("click"),e.index.navigateBack({delta:1,fail:()=>{e.index.switchTab({url:"/pages/index/index"})}})};return e.onLoad(e=>{console.log("📱 标签闯关页面加载:",e)}),e.onMounted(()=>{v()}),(a,o)=>e.e({a:r.value},r.value?{b:e.t(r.value.completedLevels),c:e.t(r.value.totalStars)}:{},{d:l.value},l.value?{}:n.value?{f:e.t(n.value),g:e.o(v)}:{h:e.f(s.value,(a,o,l)=>e.e({a:e.t(a.name),b:a.isVip},(a.isVip,{}),{c:a.isVip&&!u.value},(a.isVip&&u.value,{}),{d:e.t(a.description||"暂无描述"),e:e.t(a.levelCount||0),f:"active"===a.status?1:"",g:e.t("active"===a.status?"可用":"维护中"),h:a.id,i:a.isVip?1:"",j:"active"===a.status?1:"",k:a.isVip&&!u.value?1:"",l:a.color||"#667eea",m:e.o(o=>(async a=>{try{if(t.audioManager.playSoundEffect("click"),console.log("🎯 选择标签:",a.name),"active"!==a.status)return void i.showError("该标签暂时不可用");if(a.isVip&&!u.value)return console.log("🔒 需要VIP权限访问标签:",a.name),void d();e.index.navigateTo({url:`/pages/tag-levels/index?tagId=${a.id}&tagName=${encodeURIComponent(a.name)}`})}catch(o){console.error("❌ 选择标签失败:",o),i.showError("进入标签关卡失败")}})(a),a.id)}))},{e:n.value,i:e.o(g)})}}),l=e._export_sfc(o,[["__scopeId","data-v-f9deb150"]]);wx.createPage(l);
