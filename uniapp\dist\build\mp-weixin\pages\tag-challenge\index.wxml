<view class="tag-challenge-container data-v-f9deb150"><view class="page-header data-v-f9deb150"><view class="header-content data-v-f9deb150"><text class="page-title data-v-f9deb150">🏷️ 标签闯关</text><text class="page-subtitle data-v-f9deb150">选择标签分类，挑战相关主题的关卡</text></view><view wx:if="{{a}}" class="header-stats data-v-f9deb150"><text class="stats-item data-v-f9deb150">总完成: {{b}}关</text><text class="stats-item data-v-f9deb150">总星级: {{c}}⭐</text></view></view><view wx:if="{{d}}" class="loading-container data-v-f9deb150"><view class="loading-spinner data-v-f9deb150"></view><text class="loading-text data-v-f9deb150">正在加载标签...</text></view><view wx:elif="{{e}}" class="error-container data-v-f9deb150"><view class="error-icon data-v-f9deb150">⚠️</view><text class="error-title data-v-f9deb150">加载失败</text><text class="error-text data-v-f9deb150">{{f}}</text><button class="retry-btn data-v-f9deb150" bindtap="{{g}}"><text class="retry-text data-v-f9deb150">重试</text></button></view><view wx:else class="tags-container data-v-f9deb150"><view class="tags-grid data-v-f9deb150"><view wx:for="{{h}}" wx:for-item="tag" wx:key="h" class="{{['tag-card', 'data-v-f9deb150', tag.i && 'tag-vip', tag.j && 'tag-active', tag.k && 'tag-locked']}}" style="{{'border-left-color:' + tag.l}}" bindtap="{{tag.m}}"><view class="tag-info data-v-f9deb150"><view class="tag-header data-v-f9deb150"><text class="tag-name data-v-f9deb150">{{tag.a}}</text><view class="tag-badges data-v-f9deb150"><view wx:if="{{tag.b}}" class="vip-badge data-v-f9deb150"><text class="vip-text data-v-f9deb150">VIP</text></view><view wx:if="{{tag.c}}" class="lock-badge data-v-f9deb150"><text class="lock-text data-v-f9deb150">🔒</text></view></view></view><text class="tag-desc data-v-f9deb150">{{tag.d}}</text><view class="level-count data-v-f9deb150"><text class="count-text data-v-f9deb150">{{tag.e}} 个关卡</text></view></view><view class="tag-status data-v-f9deb150"><view class="{{['status-dot', 'data-v-f9deb150', tag.f && 'active']}}"></view><text class="status-text data-v-f9deb150">{{tag.g}}</text></view></view></view></view><view class="bottom-actions data-v-f9deb150"><button class="back-btn data-v-f9deb150" bindtap="{{i}}"><text class="back-text data-v-f9deb150">返回首页</text></button></view></view>