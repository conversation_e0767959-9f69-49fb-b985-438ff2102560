"use strict";const e=require("../../common/vendor.js"),n=require("../../api/weixin.js"),i=require("../../utils/audio.js"),a=require("../../utils/auth.js"),t=e.defineComponent({__name:"index",setup(t){const o=e.ref(null),c=e.ref(null),r=e.ref([]),s=e.ref(!1);e.onLoad(async()=>{await a.checkLoginAndRedirect({toastMessage:"请先登录以访问会员中心",redirectUrl:"/pages/login/index?redirect="+encodeURIComponent("/pages/member-center/index")})&&l()});const l=async()=>{try{await d(),await u()}catch(e){console.error("加载页面数据失败:",e)}},d=async()=>{try{const e=await n.weixinApi.refreshUserInfo();if(e)o.value=e;else{const e=n.weixinApi.getLocalUserInfo();o.value=e}}catch(e){console.error("加载用户信息失败:",e);const i=n.weixinApi.getLocalUserInfo();o.value=i}},u=async()=>{try{const e=await n.weixinApi.getDailyStatus();c.value=e}catch(e){console.error("加载每日状态失败:",e)}},p=()=>{var e,n;return((null==(e=o.value)?void 0:e.nickname)||(null==(n=o.value)?void 0:n.maskedPhone)||"游戏玩家").charAt(0).toUpperCase()},v=()=>{i.audioManager.playSoundEffect("click"),e.index.navigateTo({url:"/pages/activation-code/index"})},x=async()=>{try{i.audioManager.playSoundEffect("click"),s.value=!0;const a=await n.weixinApi.getVipPackages();r.value=a;const t=a.map(e=>`${e.name} - ¥${(e.price/100).toFixed(2)}`);e.index.showActionSheet({itemList:t,success:e=>{const n=a[e.tapIndex];n&&w(n)}})}catch(a){console.error("加载VIP套餐失败:",a),e.index.showToast({title:"加载套餐失败",icon:"none"})}finally{s.value=!1}},w=n=>{const i=(n.price/100).toFixed(2);e.index.showModal({title:"确认购买",content:`${n.name}\n价格：¥${i}\n时长：${n.duration}天\n\n${n.description}`,showCancel:!0,cancelText:"取消",confirmText:"立即支付",success:e=>{e.confirm&&h(n)}})},h=async i=>{try{e.index.showLoading({title:"正在创建订单..."});await n.weixinApi.requestPayment(i.id)&&(await d(),await u(),e.index.showModal({title:"支付成功",content:`恭喜您成为VIP会员！\n已获得${i.duration}天VIP特权`,showCancel:!1,confirmText:"太棒了"}))}catch(a){console.error("VIP支付失败:",a),e.index.showModal({title:"支付失败",content:"支付过程中出现问题，请稍后重试",showCancel:!1,confirmText:"知道了"})}finally{e.index.hideLoading()}};return(n,i)=>{var a,t,r,s;return e.e({a:o.value},o.value?e.e({b:e.t(p()),c:e.t(o.value.nickname||o.value.maskedPhone||"游戏玩家"),d:e.t(o.value.id),e:null==(a=c.value)?void 0:a.isVip},(null==(t=c.value)||t.isVip,{})):{},{f:null==(r=c.value)?void 0:r.isVip},(null==(s=c.value)?void 0:s.isVip)?{}:{g:e.o(x)},{h:e.o(v)})}}}),o=e._export_sfc(t,[["__scopeId","data-v-d57531e2"]]);wx.createPage(o);
