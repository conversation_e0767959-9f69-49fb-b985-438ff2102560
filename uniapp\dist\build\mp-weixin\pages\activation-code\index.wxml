<view class="activation-code-container data-v-86a49c34"><view class="page-header data-v-86a49c34"><text class="page-title data-v-86a49c34">激活码兑换</text><text class="page-subtitle data-v-86a49c34">输入激活码获得VIP权限</text></view><view class="input-section data-v-86a49c34"><view class="input-container data-v-86a49c34"><text class="input-label data-v-86a49c34">激活码</text><input class="code-input data-v-86a49c34" placeholder="请输入激活码" maxlength="20" disabled="{{a}}" value="{{b}}" bindinput="{{c}}"/></view><button class="{{['redeem-btn', 'data-v-86a49c34', e && 'redeem-btn-disabled']}}" disabled="{{f}}" bindtap="{{g}}"><text class="redeem-btn-text data-v-86a49c34">{{d}}</text></button></view><view class="info-section data-v-86a49c34"><view class="info-card data-v-86a49c34"><text class="info-title data-v-86a49c34">💎 VIP特权</text><view class="privilege-list data-v-86a49c34"><text class="privilege-item data-v-86a49c34">• 无限制解锁关卡</text><text class="privilege-item data-v-86a49c34">• 专享VIP标签关卡</text><text class="privilege-item data-v-86a49c34">• 优先体验新功能</text><text class="privilege-item data-v-86a49c34">• 专属客服支持</text></view></view><view class="info-card data-v-86a49c34"><text class="info-title data-v-86a49c34">📝 使用说明</text><view class="instruction-list data-v-86a49c34"><text class="instruction-item data-v-86a49c34">1. 输入有效的激活码</text><text class="instruction-item data-v-86a49c34">2. 点击"立即兑换"按钮</text><text class="instruction-item data-v-86a49c34">3. 兑换成功后即可享受VIP特权</text><text class="instruction-item data-v-86a49c34">4. 激活码仅可使用一次</text></view></view></view><view wx:if="{{h}}" class="modal-overlay data-v-86a49c34"><view class="modal-content data-v-86a49c34"><view class="result-icon data-v-86a49c34"><text class="icon data-v-86a49c34">{{i}}</text></view><text class="result-title data-v-86a49c34">{{j}}</text><text class="result-message data-v-86a49c34">{{k}}</text><view wx:if="{{l}}" class="package-info data-v-86a49c34"><text class="package-name data-v-86a49c34">{{m}}</text><text class="package-desc data-v-86a49c34">{{n}}</text></view><button class="modal-btn data-v-86a49c34" bindtap="{{o}}"><text class="modal-btn-text data-v-86a49c34">确定</text></button></view></view><view class="bottom-actions data-v-86a49c34"><button class="back-btn data-v-86a49c34" bindtap="{{p}}"><text class="back-text data-v-86a49c34">返回</text></button></view></view>