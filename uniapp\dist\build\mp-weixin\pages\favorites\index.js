"use strict";const e=require("../../common/vendor.js"),a=require("../../api/weixin.js"),o=require("../../utils/audio.js"),i=require("../../utils/auth.js"),l=e.defineComponent({__name:"index",setup(l){const n=e.ref(!1),t=e.ref(null),s=e.ref([]),c=e.ref(null);e.computed(()=>s.value.map((e,a)=>({...e,levelNumber:String(a+1).padStart(2,"0"),locked:!e.isUnlocked,completed:e.isCompleted}))),e.onLoad(async()=>{var o;if(await i.checkLoginAndRedirect({toastMessage:"请先登录以查看收藏",redirectUrl:"/pages/login/index"})){try{if(console.log("🔍 检查VIP状态..."),c.value=await a.weixinApi.getDailyStatus(),!(null==(o=c.value)?void 0:o.isVip))return console.log("❌ 非VIP用户，无法访问收藏功能"),void e.index.showModal({title:"收藏功能",content:"收藏为会员专享功能，请先开通会员",showCancel:!1,confirmText:"返回",success:()=>{e.index.navigateBack()}});console.log("✅ VIP用户，可以访问收藏功能")}catch(l){return console.error("获取每日状态失败:",l),void e.index.showModal({title:"加载失败",content:"无法验证会员状态，请稍后重试",showCancel:!1,confirmText:"返回",success:()=>{e.index.navigateBack()}})}await d()}});const d=async()=>{try{n.value=!0,t.value=null,console.log("🔄 开始加载收藏列表...");const e=await a.weixinApi.getUserFavorites();console.log("📦 收藏列表API响应:",e),e&&e.favorites?(s.value=e.favorites,console.log("✅ 收藏列表加载成功，数量:",s.value.length)):(s.value=[],console.log("⚠️ 收藏列表响应格式异常，设置为空数组")),0===s.value.length&&console.log("📭 收藏列表为空，显示空状态页面")}catch(e){console.error("❌ 加载收藏列表失败:",e),e&&"object"==typeof e&&"message"in e&&console.log("错误详情:",e.message),t.value="加载失败，请重试",s.value=[]}finally{n.value=!1,console.log("🏁 收藏列表加载完成，最终状态:",{loading:n.value,error:t.value,favoritesCount:s.value.length})}},r=()=>{o.audioManager.playSoundEffect("click"),e.index.navigateTo({url:"/pages/level-selection/index"})},u=()=>{o.audioManager.playSoundEffect("click"),e.index.navigateTo({url:"/pages/tag-selection/index"})},g=()=>{o.audioManager.playSoundEffect("click"),e.index.navigateBack()};return(i,l)=>e.e({a:n.value},n.value?{}:t.value?{c:e.t(t.value),d:e.o(d)}:0===s.value.length?{f:e.o(r),g:e.o(u)}:{h:e.t(s.value.length),i:e.f(s.value,(i,l,n)=>e.e({a:e.t(l+1),b:e.t(i.name),c:e.t(i.description),d:i.tagIds&&i.tagIds.length>0},i.tagIds&&i.tagIds.length>0?{e:e.f(i.tagIds.slice(0,2),(a,o,i)=>e.e({a:e.t(a.name),b:a.isVip},(a.isVip,{}),{c:a.id,d:a.isVip?1:""}))}:{},{f:i.isUnlocked},i.isUnlocked?{}:i.isCompleted?{h:e.f(i.userStars||0,(e,a,o)=>({a:e}))}:{},{g:i.isCompleted,i:e.o(l=>(async i=>{try{o.audioManager.playSoundEffect("click");const l=await a.weixinApi.removeFavorite(i.id);if(!l.success)throw new Error(l.message||"取消收藏失败");s.value=s.value.filter(e=>e.id!==i.id),o.audioManager.playSoundEffect("complete"),e.index.showToast({title:"已取消收藏",icon:"success",duration:1500})}catch(l){console.error("取消收藏失败:",l),o.audioManager.playSoundEffect("fail"),e.index.showToast({title:"取消收藏失败",icon:"none",duration:1500})}})(i),i.id),j:i.id,k:i.isUnlocked?1:"",l:i.isCompleted?1:"",m:e.o(a=>(async a=>{if(a.locked)return o.audioManager.playSoundEffect("fail"),void e.index.showToast({title:"该关卡尚未解锁",icon:"none",duration:1500});o.audioManager.playSoundEffect("click"),console.log("Selected level:",a),e.index.setStorageSync("selectedLevel",JSON.stringify(a)),e.index.navigateTo({url:"/pages/game/index"})})(i),i.id)}))},{b:t.value,e:0===s.value.length,j:e.o(g)})}}),n=e._export_sfc(l,[["__scopeId","data-v-6f195d7c"]]);wx.createPage(n);
