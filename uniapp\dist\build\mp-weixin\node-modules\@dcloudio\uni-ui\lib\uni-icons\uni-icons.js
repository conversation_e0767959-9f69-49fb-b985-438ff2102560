"use strict";const t=require("../../../../../common/vendor.js"),e={name:"UniIcons",emits:["click"],props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customPrefix:{type:String,default:""},fontFamily:{type:String,default:""}},data:()=>({icons:t.fontData}),computed:{unicode(){let t=this.icons.find(t=>t.font_class===this.type);return t?t.unicode:""},iconSize(){return"number"==typeof(t=this.size)||/^[0-9]*$/g.test(t)?t+"px":t;var t},styleObj(){return""!==this.fontFamily?`color: ${this.color}; font-size: ${this.iconSize}; font-family: ${this.fontFamily};`:`color: ${this.color}; font-size: ${this.iconSize};`}},methods:{_onClick(){this.$emit("click")}}};const i=t._export_sfc(e,[["render",function(e,i,o,n,s,c){return{a:t.s(c.styleObj),b:t.n("uniui-"+o.type),c:t.n(o.customPrefix),d:t.n(o.customPrefix?o.type:""),e:t.o((...t)=>c._onClick&&c._onClick(...t))}}]]);wx.createComponent(i);
