<view wx:if="{{a}}" class="settings-modal-overlay data-v-3f2d524c" bindtap="{{l}}"><view class="settings-modal data-v-3f2d524c" catchtap="{{k}}"><view class="modal-header data-v-3f2d524c"><text class="modal-title data-v-3f2d524c">⚙️ 游戏设置</text><view class="close-btn data-v-3f2d524c" bindtap="{{b}}"><text class="close-icon data-v-3f2d524c">✕</text></view></view><view class="modal-content data-v-3f2d524c"><view class="setting-item data-v-3f2d524c"><view class="setting-info data-v-3f2d524c"><text class="setting-label data-v-3f2d524c">🎵 背景音乐</text><text class="setting-desc data-v-3f2d524c">开启后在游戏中播放背景音乐</text></view><switch class="data-v-3f2d524c" checked="{{c}}" bindchange="{{d}}" color="#667eea"/></view><view class="setting-item data-v-3f2d524c"><view class="setting-info data-v-3f2d524c"><text class="setting-label data-v-3f2d524c">🔊 游戏音效</text><text class="setting-desc data-v-3f2d524c">开启后播放点击、成功等音效</text></view><switch class="data-v-3f2d524c" checked="{{e}}" bindchange="{{f}}" color="#667eea"/></view><view class="setting-item data-v-3f2d524c"><view class="setting-info data-v-3f2d524c"><text class="setting-label data-v-3f2d524c">📳 触觉反馈</text><text class="setting-desc data-v-3f2d524c">开启后在特定操作时震动</text></view><switch class="data-v-3f2d524c" checked="{{g}}" bindchange="{{h}}" color="#667eea"/></view></view><view class="modal-footer data-v-3f2d524c"><button class="test-btn data-v-3f2d524c" bindtap="{{i}}"><text class="test-btn-text data-v-3f2d524c">🎧 测试音效</text></button><button class="confirm-btn data-v-3f2d524c" bindtap="{{j}}"><text class="confirm-btn-text data-v-3f2d524c">确定</text></button></view></view></view>