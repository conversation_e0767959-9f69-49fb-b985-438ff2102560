"use strict";const n=require("../common/vendor.js"),e=require("./env.js");function o(){const o="development"===e.getCurrentEnvironment();try{const e=n.index.getAccountInfoSync().miniProgram.envVersion;return o&&"develop"===e}catch(r){return o&&console.warn("获取微信小程序环境信息失败:",r),!1}return o}function r(){return o()}exports.debugError=function(n,...e){r()&&console.error(`[DEBUG ERROR] ${n}`,...e)},exports.debugLog=function(n,...e){r()&&console.log(`[DEBUG] ${n}`,...e)},exports.getDebugEnvironmentInfo=function(){const r=e.getCurrentEnvironment(),t=o();let i="unknown",u={};i="mp-weixin";try{const e=n.index.getAccountInfoSync();u={envVersion:e.miniProgram.envVersion,version:e.miniProgram.version}}catch(c){u={error:"Failed to get account info"}}return{environment:r,shouldShowDebug:t,platform:"mp-weixin",details:u}},exports.shouldShowDebugButtons=function(){return o()},exports.shouldShowDebugPages=function(){return o()};
