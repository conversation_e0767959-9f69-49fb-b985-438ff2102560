"use strict";const e=require("../../common/vendor.js"),n=require("../../api/weixin.js"),i=require("../../api/utils.js"),o=require("../../utils/share.js"),a=require("../../utils/audio.js"),l=require("../../utils/debug-control.js"),t=require("../../utils/auth.js"),c=require("../../composables/useGlobalConfig.js");if(!Array){e.resolveComponent("uni-icons")()}Math||((()=>"../../node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons.js")+s)();const s=()=>"../../components/SettingsModal.js",r=e.defineComponent({__name:"index",setup(s){const r=e.reactive({level:[],currentLevel:null,...i.createLoadingState()}),d=e.ref(null),u=e.ref(null),g=e.ref(null),h=e.ref([]),p=e.ref(!1),v=e.ref(null),f=e.ref(!1),w=e.ref({backgroundMusic:!0,soundEffects:!0,vibration:!0}),{getBackgroundMusicUrl:m,showHelpModal:y,initializeGlobalConfig:x}=c.useGlobalConfig(),k=e.ref(!1),S=e.ref(!1);let M="",b=!1;e.computed(()=>r.level.map((e,n)=>({id:e.id,levelNumber:String(n+1).padStart(2,"0"),name:e.name,description:e.description,library:T(e),locked:!e.isUnlocked,completed:e.isCompleted})));const I=[{id:1,name:"小学基础词汇",words:[{english:"apple",chinese:"苹果",phonetic:"/ˈæpl/"},{english:"book",chinese:"书",phonetic:"/bʊk/"},{english:"cat",chinese:"猫",phonetic:"/kæt/"},{english:"dog",chinese:"狗",phonetic:"/dɔːɡ/"},{english:"egg",chinese:"鸡蛋",phonetic:"/eɡ/"},{english:"fish",chinese:"鱼",phonetic:"/fɪʃ/"},{english:"green",chinese:"绿色",phonetic:"/ɡriːn/"},{english:"house",chinese:"房子",phonetic:"/haʊs/"},{english:"ice",chinese:"冰",phonetic:"/aɪs/"},{english:"jump",chinese:"跳",phonetic:"/dʒʌmp/"}]},{id:2,name:"动物世界",words:[{english:"lion",chinese:"狮子",phonetic:"/ˈlaɪən/"},{english:"tiger",chinese:"老虎",phonetic:"/ˈtaɪɡər/"},{english:"elephant",chinese:"大象",phonetic:"/ˈelɪfənt/"},{english:"monkey",chinese:"猴子",phonetic:"/ˈmʌŋki/"},{english:"rabbit",chinese:"兔子",phonetic:"/ˈræbɪt/"},{english:"bird",chinese:"鸟",phonetic:"/bɜːrd/"},{english:"horse",chinese:"马",phonetic:"/hɔːrs/"},{english:"cow",chinese:"牛",phonetic:"/kaʊ/"}]},{id:3,name:"颜色彩虹",words:[{english:"red",chinese:"红色",phonetic:"/red/"},{english:"blue",chinese:"蓝色",phonetic:"/bluː/"},{english:"yellow",chinese:"黄色",phonetic:"/ˈjeloʊ/"},{english:"green",chinese:"绿色",phonetic:"/ɡriːn/"},{english:"purple",chinese:"紫色",phonetic:"/ˈpɜːrpl/"},{english:"orange",chinese:"橙色",phonetic:"/ˈɔːrɪndʒ/"},{english:"pink",chinese:"粉色",phonetic:"/pɪŋk/"},{english:"black",chinese:"黑色",phonetic:"/blæk/"},{english:"white",chinese:"白色",phonetic:"/waɪt/"}]},{id:4,name:"数字王国",words:[{english:"one",chinese:"一",phonetic:"/wʌn/"},{english:"two",chinese:"二",phonetic:"/tuː/"},{english:"three",chinese:"三",phonetic:"/θriː/"},{english:"four",chinese:"四",phonetic:"/fɔːr/"},{english:"five",chinese:"五",phonetic:"/faɪv/"},{english:"six",chinese:"六",phonetic:"/sɪks/"},{english:"seven",chinese:"七",phonetic:"/ˈsevn/"},{english:"eight",chinese:"八",phonetic:"/eɪt/"},{english:"nine",chinese:"九",phonetic:"/naɪn/"},{english:"ten",chinese:"十",phonetic:"/ten/"}]}],T=e=>{const n=(e.difficulty-1)%I.length;return I[n]};e.onMounted(async()=>{await x(),await V(),H(),U(),j(),D()}),e.onShow(()=>{console.log("首页显示"),a.audioManager.onPageShow();a.audioManager.getSettings().backgroundMusic&&a.audioManager.playBackgroundMusic("main")}),e.onHide(()=>{console.log("首页隐藏"),a.audioManager.onPageHide()});const U=()=>{const n=e.index.getStorageSync("userInfo");M=n||"",setInterval(async()=>{const n=e.index.getStorageSync("userInfo");n!==M&&(console.log("检测到用户信息更新，刷新首页数据"),M=n,await L())},2e3),console.log("页面可见性检查已启动")},V=async()=>{await i.withLoading(r,async()=>{await P(),await Z()},{errorMessage:"页面加载失败，请重试"})},L=async()=>{if(!S.value)try{S.value=!0,console.log("开始刷新首页数据..."),await P(),await ne(),console.log("首页数据刷新完成")}catch(e){console.error("刷新页面数据失败:",e)}finally{S.value=!1}},P=async()=>{try{try{const e=await n.weixinApi.refreshUserInfo();if(e)return d.value=e,console.log("从服务器获取到最新用户信息:",e),await A(),void(await C())}catch(e){console.warn("从服务器获取用户信息失败，使用本地缓存:",e)}const o=n.weixinApi.getLocalUserInfo();if(o){d.value=o,console.log("使用本地缓存用户信息:",o);try{await A(),await C()}catch(i){console.warn("加载每日状态和星级统计失败:",i)}}else console.warn("未找到用户信息，可能需要重新登录")}catch(i){console.error("加载用户信息失败:",i)}},A=async()=>{try{const e=await n.weixinApi.getDailyStatus();u.value=e,console.log("每日状态加载成功:",e),$(e)}catch(e){console.error("加载每日状态失败:",e)}},C=async()=>{try{const e=await n.weixinApi.getUserStarStats();v.value=e,console.log("用户星级统计加载成功:",e)}catch(e){console.error("加载用户星级统计失败:",e),v.value={totalStars:0,threeStarLevels:0,twoStarLevels:0,oneStarLevels:0,completedLevels:0}}},$=n=>{if(n.isVip)return void console.log("VIP用户，无解锁限制");const i=n.remainingUnlocks>0?`今日还可解锁 ${n.remainingUnlocks} 次`:"今日解锁次数已用完";console.log(`每日状态: ${i}`),n.remainingUnlocks<=3&&!n.dailyShared&&setTimeout(()=>{e.index.showModal({title:"解锁提醒",content:`${i}，分享游戏可获得5次额外解锁机会！`,showCancel:!0,cancelText:"稍后再说",confirmText:"立即分享",success:e=>{e.confirm&&_()}})},1e3)},_=()=>{e.index.showShareMenu({withShareTicket:!0})},j=()=>{try{const e=a.audioManager.getSettings();if(w.value={...e},console.log("音频设置初始化完成:",e),e.backgroundMusic){const e=m("main");console.log("播放首页背景音乐:",e),a.audioManager.playBackgroundMusic("main",e)}}catch(e){console.error("初始化音频设置失败:",e)}},D=()=>{try{k.value=l.shouldShowDebugButtons(),console.log("开发环境检测结果:",k.value)}catch(e){console.error("检测开发环境失败:",e),k.value=!1}},E=()=>{a.audioManager.playSoundEffect("click"),f.value=!0,console.log("显示设置弹窗")},q=()=>{f.value=!1,console.log("关闭设置弹窗")},R=e=>{w.value={...e},console.log("设置已更新:",e),e.backgroundMusic!==w.value.backgroundMusic&&(e.backgroundMusic?a.audioManager.playBackgroundMusic("main"):a.audioManager.stopBackgroundMusic())},B=()=>{a.audioManager.playSoundEffect("click"),y()},O=async()=>{a.audioManager.playSoundEffect("click");await t.checkLoginAndRedirect({toastMessage:"请先登录以开始游戏",redirectUrl:"/pages/login/index?redirect="+encodeURIComponent("/pages/level-selection/index")})&&F()},F=()=>{const n=G();e.index.navigateTo({url:"/pages/level-selection/index"+(n?"?scrollToLevel="+n:"")})},G=()=>{try{const n=e.index.getStorageSync("selectedLevel");if(n){return JSON.parse(n).id}}catch(n){console.error("获取上次关卡ID失败:",n)}return null},H=()=>{try{const n=e.index.getStorageSync("selectedLevel");if(n){const e=JSON.parse(n);g.value=e,console.log("加载上次关卡信息:",e)}else g.value=null}catch(n){console.error("加载上次关卡信息失败:",n),g.value=null}},z=async()=>{var n;a.audioManager.playSoundEffect("click");if(await t.checkLoginAndRedirect({toastMessage:"请先登录以访问收藏夹",redirectUrl:"/pages/login/index?redirect="+encodeURIComponent("/pages/member-center/index")}))try{u.value||(console.log("🔍 获取每日状态以检查VIP权限..."),await A()),(null==(n=u.value)?void 0:n.isVip)?(console.log("✅ VIP用户，打开收藏夹"),e.index.navigateTo({url:"/pages/favorites/index"})):(console.log("❌ 普通用户，显示VIP提示"),N())}catch(i){console.error("检查VIP状态失败:",i),N()}},N=()=>{e.index.showModal({title:"收藏功能",content:"收藏为会员专享，解锁所有主题可享受更全面的功能！立即开通会员？",confirmText:"去开通",cancelText:"取消",success:n=>{n.confirm?(a.audioManager.playSoundEffect("click"),e.index.navigateTo({url:"/pages/member-center/index"})):a.audioManager.playSoundEffect("click")}})},J=async()=>{var n;a.audioManager.playSoundEffect("click");if(await t.checkLoginAndRedirect({toastMessage:"请先登录以进行标签闯关",redirectUrl:"/pages/login/index?redirect="+encodeURIComponent("/pages/tag-challenge/index")}))try{u.value||(console.log("🔍 获取每日状态以检查VIP权限..."),await A()),(null==(n=u.value)?void 0:n.isVip)?(console.log("✅ VIP用户，允许访问标签闯关"),e.index.navigateTo({url:"/pages/tag-challenge/index"})):(console.log("❌ 非VIP用户，显示权限提示"),K())}catch(i){console.error("❌ 检查VIP状态失败:",i),e.index.showToast({title:"检查权限失败，请重试",icon:"none",duration:1500})}},K=()=>{e.index.showModal({title:"VIP专享功能",content:"标签闯关为VIP专享功能，开通VIP会员即可畅玩所有标签关卡！",confirmText:"开通VIP",cancelText:"取消",success:n=>{n.confirm&&e.index.navigateTo({url:"/pages/member-center/index"})}})},Q=async()=>{a.audioManager.playSoundEffect("click");await t.checkLoginAndRedirect({toastMessage:"请先登录以访问会员中心",redirectUrl:"/pages/login/index"})&&e.index.navigateTo({url:"/pages/member-center/index"})},W=async()=>{try{a.audioManager.playSoundEffect("click"),p.value=!0;const i=await n.weixinApi.getVipPackages();h.value=i,console.log("VIP套餐列表:",i);const o=i.map(e=>`${e.name} - ¥${(e.price/100).toFixed(2)}`);e.index.showActionSheet({itemList:o,success:e=>{const n=i[e.tapIndex];n&&X(n)},fail:e=>{console.log("用户取消选择套餐:",e)}})}catch(i){console.error("加载VIP套餐失败:",i),e.index.showToast({title:"加载套餐失败",icon:"none",duration:2e3})}finally{p.value=!1}},X=n=>{const i=(n.price/100).toFixed(2);e.index.showModal({title:"确认购买",content:`${n.name}\n价格：¥${i}\n时长：${n.duration}天\n\n${n.description}`,showCancel:!0,cancelText:"取消",confirmText:"立即支付",success:e=>{e.confirm&&Y(n)}})},Y=async i=>{try{e.index.showLoading({title:"正在创建订单..."});await n.weixinApi.requestPayment(i.id)&&(await P(),await A(),e.index.showModal({title:"支付成功",content:`恭喜您成为VIP会员！\n已获得${i.duration}天VIP特权\n现在可以无限制解锁所有关卡！`,showCancel:!1,confirmText:"开始游戏"}))}catch(o){if(console.error("VIP支付失败:",o),o&&"object"==typeof o&&"errMsg"in o){if(o.errMsg.includes("cancel"))return}e.index.showModal({title:"支付失败",content:"支付过程中出现问题，请稍后重试。如有疑问请联系客服。",showCancel:!0,cancelText:"稍后重试",confirmText:"联系客服"})}finally{e.index.hideLoading()}},Z=async()=>{try{const e=await n.weixinApi.getLevels();r.level=e,console.log("关卡列表加载成功:",e)}catch(e){console.error("加载关卡列表失败:",e),console.log("使用本地备用关卡数据"),r.level=ee()}},ee=()=>[{id:"fallback-level-1",name:"第1关 - 基础词汇",difficulty:1,description:"小学基础词汇练习",isUnlocked:!0,isCompleted:!1,createdAt:(new Date).toISOString()},{id:"fallback-level-2",name:"第2关 - 动物世界",difficulty:2,description:"认识可爱的动物",isUnlocked:!0,isCompleted:!1,createdAt:(new Date).toISOString()},{id:"fallback-level-3",name:"第3关 - 颜色彩虹",difficulty:3,description:"学习各种颜色",isUnlocked:!1,isCompleted:!1,createdAt:(new Date).toISOString()},{id:"fallback-level-4",name:"第4关 - 数字王国",difficulty:4,description:"掌握数字表达",isUnlocked:!1,isCompleted:!1,createdAt:(new Date).toISOString()}],ne=async()=>{await Z()},ie=()=>{e.index.navigateTo({url:"/pages/debug/index"})},oe=n=>{try{const i=(new Date).toDateString(),o=`daily_share_reward_${n}_${i}`;e.index.setStorageSync(o,!0),console.log(`标记每日分享奖励完成 - 用户: ${n}, 日期: ${i}`)}catch(i){console.error("标记每日分享奖励失败:",i)}},ae=async i=>{var o;try{if(!(null==(o=d.value)?void 0:o.id))return void console.warn("用户信息不存在，无法获取分享奖励");if(b)return void console.log("分享奖励正在处理中，跳过重复请求");if((n=>{try{const i=(new Date).toDateString(),o=`daily_share_reward_${n}_${i}`,a=e.index.getStorageSync(o);return console.log(`检查每日分享奖励状态 - 用户: ${n}, 日期: ${i}, 已分享: ${!!a}`),!!a}catch(i){return console.error("检查每日分享奖励状态失败:",i),!1}})(d.value.id))return console.log("今日已获取过分享奖励，跳过本次请求"),void e.index.showToast({title:"今日已获得分享奖励",icon:"none",duration:2e3});b=!0,console.log("开始处理分享奖励:",i),setTimeout(async()=>{try{const i=await n.weixinApi.shareForReward();"success"===i.status?(oe(d.value.id),await A(),e.index.showModal({title:"分享奖励",content:`${i.message}今日分享奖励已领取完毕。`,showCancel:!1,confirmText:"太棒了",success:()=>{console.log("分享奖励提示已显示")}}),console.log("分享奖励获取成功:",i)):"already_shared"===i.status?(console.log("今日已分享过:",i.message),oe(d.value.id),e.index.showToast({title:i.message,icon:"none",duration:2e3})):(console.log("分享奖励获取失败:",i.message),e.index.showToast({title:i.message||"分享失败，请重试",icon:"none",duration:2e3}))}catch(i){console.error("获取分享奖励失败:",i)}finally{b=!1,console.log("分享奖励处理完成，重置状态")}},2e3)}catch(a){console.error("处理分享奖励失败:",a),b=!1}};return e.onShareAppMessage(e=>{var n;const i=o.shareUtils.handleShareAppMessage(e,{page:"pages/index/index",userId:null==(n=d.value)?void 0:n.id});return"menu"===e.from&&ae(e),{title:"趣护消消乐",path:"/pages/index/index",imageUrl:"",promise:i}}),(n,i)=>{var o,a,l,t,c;return e.e({a:d.value},d.value?e.e({b:v.value},v.value?{c:e.t(v.value.totalStars),d:e.t(v.value.threeStarLevels),e:e.t(v.value.completedLevels)}:{},{f:u.value&&!u.value.isVip},u.value&&!u.value.isVip?e.e({g:e.t(u.value.dailyUnlockCount),h:e.t(u.value.dailyUnlockLimit),i:u.value.remainingUnlocks>0},u.value.remainingUnlocks>0?{j:e.t(u.value.remainingUnlocks)}:{},{k:!u.value.dailyShared&&u.value.remainingUnlocks<=3},(!u.value.dailyShared&&u.value.remainingUnlocks,{})):{},{l:u.value&&u.value.isVip},(u.value&&u.value.isVip,{}),{m:!(null==(o=u.value)?void 0:o.isVip)},(null==(a=u.value)?void 0:a.isVip)?{}:{n:e.o(W)}):{},{o:e.t(g.value?"继续上次的游戏进度":"选择关卡开始你的学习之旅"),p:e.t((null==(l=d.value)?void 0:l.unlockedLevels)||0),q:e.t((null==(c=null==(t=d.value)?void 0:t.completedLevelIds)?void 0:c.length)||0),r:g.value},g.value?{s:e.t(g.value.name||"第"+g.value.id+"关")}:{},{t:e.t(g.value?"继续游戏":"选择关卡"),v:e.o(O),w:e.o(z),x:e.p({type:"help",color:"#666",size:"20"}),y:e.o(B),z:e.o(J),A:e.o(Q),B:e.o(E),C:e.o(q),D:e.o(R),E:e.p({visible:f.value}),F:k.value},k.value?{G:e.o(ie)}:{})}}});r.__runtimeHooks=2;const d=e._export_sfc(r,[["__scopeId","data-v-1be7113f"]]);wx.createPage(d);
