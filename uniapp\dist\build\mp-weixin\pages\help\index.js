"use strict";const e=require("../../common/vendor.js"),o=require("../../composables/useGlobalConfig.js"),n=e.defineComponent({__name:"index",setup(n){const a=e.ref(""),l=e.ref(!0),{helpPageConfig:s,fetchGlobalConfig:r}=o.useGlobalConfig();e.onMounted(async()=>{await t()});const t=async()=>{l.value=!0;try{await r();const e=s.value;console.log(e,"helpConfig.url"),a.value=e,l.value=!1}catch(e){console.error("加载帮助内容失败:",e),l.value=!1}};return(e,o)=>({a:a.value})}});wx.createPage(n);
