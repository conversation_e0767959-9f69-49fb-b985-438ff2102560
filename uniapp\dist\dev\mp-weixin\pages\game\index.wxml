<view class="page-box data-v-131fc1ab"><view class="game-page-container data-v-131fc1ab"><view wx:if="{{a}}" class="level-detail-info card data-v-131fc1ab"><view class="level-header data-v-131fc1ab"><text class="level-name data-v-131fc1ab">{{b}}</text></view><view class="level-stats data-v-131fc1ab"><text class="stats-item data-v-131fc1ab">词组数量: {{c}}</text><text wx:if="{{d}}" class="stats-item data-v-131fc1ab">已完成</text><text wx:else class="stats-item data-v-131fc1ab">🎯 挑战中</text></view></view><view wx:elif="{{e}}" class="selected-library-info card data-v-131fc1ab"><text class="library-name data-v-131fc1ab">{{f}}</text></view><view wx:if="{{g}}" class="game-area card data-v-131fc1ab"><view class="game-info-bar data-v-131fc1ab"><text class="{{['game-timer', 'data-v-131fc1ab', i && 'time-warning']}}"> ⏱️ {{h}}</text><text wx:if="{{j}}" class="sync-status data-v-131fc1ab">同步中...</text><text wx:if="{{k}}" class="h5-mock-tip data-v-131fc1ab">H5演示</text><view wx:if="{{l}}" class="debug-btn data-v-131fc1ab" bindtap="{{n}}"><text class="debug-btn-text data-v-131fc1ab">{{m}}</text></view><view wx:if="{{o}}" class="grid-debug-btn data-v-131fc1ab" bindtap="{{q}}"><text class="grid-debug-btn-text data-v-131fc1ab">{{p}}</text></view><view class="{{['replay-btn', 'data-v-131fc1ab', s && 'replay-btn-disabled']}}" bindtap="{{t}}"><text class="replay-btn-text data-v-131fc1ab">{{r}}</text></view></view><view wx:if="{{v}}" class="loading-container data-v-131fc1ab"><view class="loading-spinner data-v-131fc1ab"></view><text class="loading-text data-v-131fc1ab">正在初始化游戏...</text></view><view wx:elif="{{w}}" class="loading-container data-v-131fc1ab"><view class="loading-spinner data-v-131fc1ab"></view><text class="loading-text data-v-131fc1ab">正在计算卡片位置...</text></view><view wx:elif="{{x}}" class="loading-container data-v-131fc1ab"><view class="loading-spinner data-v-131fc1ab"></view><text class="loading-text data-v-131fc1ab">准备渲染游戏...</text></view><view wx:elif="{{y}}" class="game-loading-container data-v-131fc1ab"><view class="game-loading-content data-v-131fc1ab"><view class="game-loading-spinner data-v-131fc1ab"><view class="spinner-ring data-v-131fc1ab"></view><view class="spinner-ring data-v-131fc1ab"></view><view class="spinner-ring data-v-131fc1ab"></view></view><text class="game-loading-title data-v-131fc1ab">{{z}}</text><text class="game-loading-subtitle data-v-131fc1ab">{{A}}</text><view class="loading-progress data-v-131fc1ab"><view class="progress-bar data-v-131fc1ab"><view class="progress-fill data-v-131fc1ab" style="{{'width:' + B}}"></view></view><text class="progress-text data-v-131fc1ab">{{C}}%</text></view></view></view><view wx:elif="{{D}}" class="error-container data-v-131fc1ab"><text class="error-text data-v-131fc1ab">游戏初始化失败</text><view class="retry-btn data-v-131fc1ab" bindtap="{{E}}"><text class="retry-btn-text data-v-131fc1ab">重试</text></view></view><view wx:elif="{{F}}" class="{{['game-board', 'data-v-131fc1ab', K && 'checking-match']}}"><view wx:if="{{G}}" class="grid-overlay data-v-131fc1ab"><view wx:for="{{H}}" wx:for-item="grid" wx:key="b" class="grid-line data-v-131fc1ab" style="{{'position:' + 'absolute' + ';' + ('left:' + grid.c) + ';' + ('top:' + grid.d) + ';' + ('width:' + grid.e) + ';' + ('height:' + grid.f) + ';' + ('border:' + '2rpx dashed rgba(255, 0, 0, 0.5)') + ';' + ('background-color:' + 'rgba(255, 0, 0, 0.1)') + ';' + ('pointer-events:' + 'none')}}"><text class="grid-label data-v-131fc1ab">{{grid.a}}</text></view></view><view wx:for="{{I}}" wx:for-item="tile" wx:key="e" class="{{['board-tile', 'data-v-131fc1ab', tile.f && 'selected', tile.g && 'matched', J && 'debug-mode', tile.h && 'tile-english', tile.i && 'tile-chinese', tile.j && 'tile-short', tile.k && 'tile-medium', tile.l && 'tile-long']}}" style="{{'background-color:' + tile.m + ';' + ('position:' + 'absolute') + ';' + ('left:' + tile.n) + ';' + ('top:' + tile.o) + ';' + ('width:' + tile.p) + ';' + ('height:' + tile.q)}}" data-position="{{tile.r}}" bindtap="{{tile.s}}"><text wx:if="{{tile.a}}" class="tile-word data-v-131fc1ab">{{tile.b}}</text><text wx:if="{{tile.c}}" class="tile-chinese-only data-v-131fc1ab">{{tile.d}}</text></view></view></view><view wx:if="{{L}}" class="modal-overlay data-v-131fc1ab"><view class="modal-content data-v-131fc1ab"><text class="modal-title data-v-131fc1ab">{{M}}</text><view wx:if="{{N}}" class="game-completion-info data-v-131fc1ab"><view class="stars-display data-v-131fc1ab"><text class="stars-label data-v-131fc1ab">获得星级</text><view class="stars-container data-v-131fc1ab"><text wx:for="{{O}}" wx:for-item="star" wx:key="a" class="{{['star-icon', 'data-v-131fc1ab', star.b && 'star-filled']}}"> ⭐ </text></view></view><view class="completion-time data-v-131fc1ab"><text class="time-label data-v-131fc1ab">完成时间</text><text class="time-value data-v-131fc1ab">{{P}}</text></view><view class="favorite-section data-v-131fc1ab"><button class="{{['favorite-btn', 'data-v-131fc1ab', S && 'favorited']}}" bindtap="{{T}}"><text class="favorite-icon data-v-131fc1ab">{{Q}}</text><text class="favorite-text data-v-131fc1ab">{{R}}</text></button></view></view><view class="modal-buttons data-v-131fc1ab"><button wx:if="{{U}}" bindtap="{{W}}" class="modal-button primary data-v-131fc1ab">{{V}}</button><button bindtap="{{X}}" class="modal-button data-v-131fc1ab">再试一次</button><button bindtap="{{Y}}" class="modal-button data-v-131fc1ab">返回首页</button></view></view></view></view><settings-modal wx:if="{{ab}}" class="data-v-131fc1ab" bindclose="{{Z}}" bindsettingsChange="{{aa}}" u-i="131fc1ab-0" bind:__l="__l" u-p="{{ab}}"/><view wx:if="{{ac}}" class="floating-debug-btn data-v-131fc1ab" bindtap="{{ad}}"><text class="debug-icon data-v-131fc1ab">🔧</text></view></view>