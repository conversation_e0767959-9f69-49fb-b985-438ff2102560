"use strict";const e=require("../common/vendor.js"),l=require("../api/weixin.js"),a=e.ref(null),n=e.ref(!1),u=e.ref(null);function o(){const o=async(e=!1)=>{if(a.value&&!e)return a.value;n.value=!0,u.value=null;try{const e=await l.weixinApi.getGlobalConfig();return a.value=e,console.log("全局配置加载成功:",e),e}finally{n.value=!1}},i=e.computed(()=>{var e;return null==(e=a.value)?void 0:e.backgroundMusicUrl}),t=e.computed(()=>{var e;return null==(e=a.value)?void 0:e.helpUrl}),r=e.computed(()=>{var e;return null==(e=a.value)?void 0:e.app});return{globalConfig:e.computed(()=>a.value),isLoading:e.computed(()=>n.value),error:e.computed(()=>u.value),backgroundMusicConfig:i,helpPageConfig:t,appConfig:r,fetchGlobalConfig:o,resetConfig:()=>{a.value=null,u.value=null,n.value=!1},getBackgroundMusicUrl:e=>i.value,navigateToHelp:()=>{const l=t.value;l.startsWith("http://")||l.startsWith("https://")?e.index.navigateTo({url:`/pages/webview/index?url=${encodeURIComponent(l)}`}):e.index.navigateTo({url:l})},showHelpModal:()=>{t.value,e.index.navigateTo({url:"/pages/help/index"})},initializeGlobalConfig:async()=>{console.log("初始化全局配置..."),await o()}}}o(),exports.useGlobalConfig=o;
