"use strict";const e=require("../common/vendor.js");require("../api/request.js");const a=require("../api/weixin.js"),r=require("../utils/debug-control.js"),o=e.defineComponent({__name:"PhoneAuth",props:{openid:{},visible:{type:Boolean}},emits:["success","error","skip"],setup(o,{emit:n}){const i=o,t=n,l=e.ref(!1),s=e.ref(!1),u=e.ref(null);e.watch(()=>i.visible,e=>{l.value=e,e&&c()}),e.onMounted(()=>{l.value=i.visible,i.visible&&c()});const c=async()=>{try{u.value={openid:"",nickname:"微信用户",avatarUrl:"",gender:0,city:"",province:"",country:""},console.log("使用默认用户信息")}catch(e){console.warn("设置默认用户资料失败:",e)}},v=async e=>{if(r.debugLog("手机号授权结果:",e),"getPhoneNumber:ok"===e.detail.errMsg){s.value=!0;try{r.debugLog("获取到加密的手机号数据:",e.detail),await d("")}catch(a){r.debugError("手机号绑定失败:",a),t("error","绑定失败，请重试")}finally{s.value=!1}}else r.debugLog("用户拒绝授权手机号"),t("error","需要授权手机号才能继续使用")},d=async e=>{var r,o;try{const n=await a.weixinApi.bindPhone({openid:i.openid,phone:e,nickname:(null==(r=u.value)?void 0:r.nickname)||"微信用户",avatarUrl:(null==(o=u.value)?void 0:o.avatarUrl)||""});console.log("手机号绑定成功:",n),l.value=!1,t("success",n)}catch(n){throw console.error("手机号绑定失败:",n),n}},p=()=>{l.value=!1,t("skip")};return(a,r)=>e.e({a:l.value},l.value?e.e({b:u.value},u.value?{c:u.value.avatarUrl||"/static/default-avatar.png",d:e.t(u.value.nickname||"微信用户")}:{},{e:e.o(v),f:s.value,g:e.o(p)}):{})}}),n=e._export_sfc(o,[["__scopeId","data-v-f11a8b7f"]]);wx.createComponent(n);
