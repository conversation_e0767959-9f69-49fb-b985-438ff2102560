<view class="debug-container data-v-21878f15"><view wx:if="{{a}}" class="access-denied data-v-21878f15"><text class="access-denied-title data-v-21878f15">访问受限</text><text class="access-denied-message data-v-21878f15">调试功能仅在开发环境下可用</text><button class="btn-back data-v-21878f15" bindtap="{{b}}">返回</button></view><view wx:else class="data-v-21878f15"><view class="debug-header data-v-21878f15"><text class="debug-title data-v-21878f15">API调试页面</text><text class="debug-env data-v-21878f15">环境: {{c}}</text></view><view class="debug-section data-v-21878f15"><text class="section-title data-v-21878f15">当前 OpenID</text><view class="info-card data-v-21878f15"><text class="info-text data-v-21878f15">{{d}}</text><button class="btn-small data-v-21878f15" bindtap="{{e}}">刷新</button></view></view><view class="debug-section data-v-21878f15"><text class="section-title data-v-21878f15">设置 OpenID</text><view class="input-group data-v-21878f15"><input class="input-field data-v-21878f15" placeholder="输入新的 openid" value="{{f}}" bindinput="{{g}}"/><button class="btn-primary data-v-21878f15" bindtap="{{h}}">设置</button></view></view><view class="debug-section data-v-21878f15"><text class="section-title data-v-21878f15">API测试</text><view class="button-group data-v-21878f15"><button class="btn-test data-v-21878f15" bindtap="{{i}}">测试获取微信code</button><button class="btn-test data-v-21878f15" bindtap="{{j}}">检查登录状态</button><button class="btn-test data-v-21878f15" bindtap="{{k}}">测试完整登录流程</button><button class="btn-test data-v-21878f15" bindtap="{{l}}">测试手机号绑定</button><button class="btn-test data-v-21878f15" bindtap="{{m}}">检查微信配置</button><button class="btn-test data-v-21878f15" bindtap="{{n}}">获取用户信息</button><button class="btn-test data-v-21878f15" bindtap="{{o}}">获取关卡列表</button><button class="btn-test data-v-21878f15" bindtap="{{p}}">获取关卡详情</button><button class="btn-test data-v-21878f15" bindtap="{{q}}">测试通关接口</button><button class="btn-test data-v-21878f15" bindtap="{{r}}">测试开始游戏</button><button class="btn-test data-v-21878f15" bindtap="{{s}}">获取分享配置</button><button class="btn-test data-v-21878f15" bindtap="{{t}}">测试微信分享</button><button class="btn-test data-v-21878f15" bindtap="{{v}}">测试系统分享</button><button class="btn-test data-v-21878f15" bindtap="{{w}}">获取分享奖励</button><button class="btn-test data-v-21878f15" bindtap="{{x}}">检查每日分享状态</button><button class="btn-test data-v-21878f15" bindtap="{{y}}">重置每日分享状态</button><button class="btn-test data-v-21878f15" bindtap="{{z}}">测试防重复执行</button><button class="btn-test data-v-21878f15" bindtap="{{A}}">测试每日状态</button><button class="btn-test data-v-21878f15" bindtap="{{B}}">测试新分享API</button><button class="btn-test data-v-21878f15" bindtap="{{C}}">测试VIP套餐</button><button class="btn-test data-v-21878f15" bindtap="{{D}}">测试VIP支付</button><button class="btn-test data-v-21878f15" bindtap="{{E}}">测试uni-icons图标</button><button class="btn-test data-v-21878f15" bindtap="{{F}}">测试音频功能</button></view></view><view class="debug-section data-v-21878f15"><text class="section-title data-v-21878f15">关卡测试参数</text><view class="input-group data-v-21878f15"><input class="input-field data-v-21878f15" placeholder="输入关卡ID (如: level-uuid-123)" value="{{G}}" bindinput="{{H}}"/><input class="input-field data-v-21878f15" placeholder="输入用户ID (如: 12345678)" value="{{I}}" bindinput="{{J}}"/></view></view><view class="debug-section data-v-21878f15"><text class="section-title data-v-21878f15">响应结果</text><view class="result-card data-v-21878f15"><text class="result-text data-v-21878f15">{{K}}</text></view></view></view></view>