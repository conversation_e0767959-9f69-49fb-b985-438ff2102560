"use strict";const e=require("../../common/vendor.js"),o=require("../../api/weixin.js"),i=require("../../utils/audio.js"),n=require("../../utils/auth.js"),s=require("../../utils/logger.js");Math||a();const a=()=>"../../components/PhoneAuth.js",t=e.defineComponent({__name:"index",setup(a){const t=e.ref(!1),l=e.ref(""),r=e.ref(!1),u=e.ref(""),c=e.ref(!1),d=e.ref("/pages/index/index");e.onLoad(e=>{s.pageLogger.login.log("登录页面加载，参数:",e),(null==e?void 0:e.redirect)&&(d.value=decodeURIComponent(e.redirect)),g()}),e.onMounted(()=>{i.audioManager.playBackgroundMusic("menu")});const g=()=>{if(!c.value&&n.isUserLoggedIn()){const o=n.getCurrentUser();console.log("用户已登录:",o),e.index.showToast({title:"已登录，正在跳转到首页",icon:"success",duration:1500}),setTimeout(()=>{h()},1500)}},v=async()=>{if(t.value||c.value)console.log("登录正在进行中，忽略重复请求");else{t.value=!0,c.value=!0,l.value="正在获取微信授权...";try{i.audioManager.playSoundEffect("click"),console.log("开始微信登录流程..."),o.weixinApi.clearLocalUserData();const n=await o.weixinApi.performWeixinLogin();if(console.log("微信登录响应:",n),"success"===n.status)l.value="登录成功，正在跳转...",e.index.showToast({title:"登录成功",icon:"success",duration:1500}),setTimeout(()=>{h()},1500);else{if("need_bind"!==n.status)throw new Error("登录失败："+n.message);l.value="需要绑定手机号",u.value=n.openid,r.value=!0}}catch(s){if(console.error("微信登录失败:",s),n.isWeixinCodeUsedError(s))e.index.showToast({title:"登录状态异常",icon:"none",duration:3e3});else{(s instanceof Error?s.message:String(s)).includes("500")||500===(null==s?void 0:s.statusCode)?e.index.showModal({title:"登录失败",content:"服务器暂时不可用，请稍后重试或联系技术支持",showCancel:!1,confirmText:"确定"}):e.index.showToast({title:"登录失败",icon:"error",duration:2e3})}}finally{t.value=!1,c.value=!1,l.value=""}}},x=o=>{console.log("手机号绑定成功:",o),r.value=!1,e.index.showToast({title:"登录成功",icon:"success",duration:1500}),setTimeout(()=>{h()},1500)},p=o=>{console.error("手机号授权失败:",o),r.value=!1,e.index.showToast({title:o,icon:"error",duration:2e3})},f=()=>{console.log("跳过手机号授权"),r.value=!1,e.index.showToast({title:"登录成功",icon:"success",duration:1500}),setTimeout(()=>{h()},1500)},h=()=>{s.pageLogger.login.log("登录成功"),e.index.switchTab({url:"/pages/index/index",success:()=>{s.pageLogger.login.log("成功跳转到首页")},fail:o=>{s.pageLogger.login.error("跳转首页失败:",o),e.index.reLaunch({url:"/pages/index/index"})}})};return(o,i)=>e.e({a:e.o(v),b:t.value,c:t.value,d:t.value},t.value?{e:e.t(l.value)}:{},{f:r.value},r.value?{g:e.o(x),h:e.o(p),i:e.o(f),j:e.p({visible:r.value,openid:u.value})}:{})}}),l=e._export_sfc(t,[["__scopeId","data-v-0e16e926"]]);wx.createPage(l);
