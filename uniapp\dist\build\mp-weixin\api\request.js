"use strict";var e=Object.defineProperty,t=(t,r,s)=>(((t,r,s)=>{r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s})(t,"symbol"!=typeof r?r+"":r,s),s);const r=require("../common/vendor.js"),s=require("./config.js"),o=require("../utils/debug-control.js");const n=new class{constructor(){t(this,"requestInterceptors",[]),t(this,"responseInterceptors",[]),t(this,"errorInterceptors",[])}addRequestInterceptor(e){this.requestInterceptors.push(e)}addResponseInterceptor(e){this.responseInterceptors.push(e)}addErrorInterceptor(e){this.errorInterceptors.push(e)}async executeRequestInterceptors(e){let t=e;for(const r of this.requestInterceptors)t=await r(t);return t}async executeResponseInterceptors(e){let t=e;for(const r of this.responseInterceptors)t=await r(t);return t}async executeErrorInterceptors(e){let t=e;for(const r of this.errorInterceptors)t=await r(t);return t}async request(e){try{const t=await this.executeRequestInterceptors(e),r=await this.sendRequest(t);return(await this.executeResponseInterceptors(r)).data}catch(t){throw await this.executeErrorInterceptors(t)}}async sendRequest(e){const{url:t,method:o="GET",data:n,header:a={},timeout:u=s.API_CONFIG.TIMEOUT,retry:c=s.API_CONFIG.RETRY_COUNT}=e;let i=null;for(let E=0;E<=c;E++)try{const e=await new Promise((e,c)=>{r.index.request({url:t,method:o,data:n,header:{"Content-Type":"application/json",...a},timeout:u,success:t=>{e(t)},fail:e=>{c(this.createError(e.errMsg||s.ERROR_MESSAGES.NETWORK_ERROR,0))}})});if(e.statusCode>=200&&e.statusCode<300)return e;throw this.createError(this.getErrorMessage(e.statusCode),e.statusCode,e.data)}catch(R){if(i=R,E===c)break;await this.delay(s.API_CONFIG.RETRY_DELAY*(E+1))}throw i}createError(e,t,r){return{message:e,statusCode:t,details:r,error:t?`HTTP ${t}`:"Request Error"}}getErrorMessage(e){switch(e){case s.HTTP_STATUS.BAD_REQUEST:return s.ERROR_MESSAGES.BAD_REQUEST;case s.HTTP_STATUS.UNAUTHORIZED:return s.ERROR_MESSAGES.UNAUTHORIZED;case s.HTTP_STATUS.NOT_FOUND:return s.ERROR_MESSAGES.NOT_FOUND;case s.HTTP_STATUS.INTERNAL_SERVER_ERROR:return s.ERROR_MESSAGES.SERVER_ERROR;default:return s.ERROR_MESSAGES.UNKNOWN_ERROR}}delay(e){return new Promise(t=>setTimeout(t,e))}get(e,t){return this.request({url:e,method:"GET",...t})}post(e,t,r){return this.request({url:e,method:"POST",data:t,...r})}put(e,t,r){return this.request({url:e,method:"PUT",data:t,...r})}delete(e,t){return this.request({url:e,method:"DELETE",...t})}patch(e,t,r){return this.request({url:e,method:"PATCH",data:t,...r})}};n.addRequestInterceptor(e=>(o.debugLog("发送请求:",e.method,e.url),e)),n.addResponseInterceptor(e=>(o.debugLog("收到响应:",e.statusCode,e.data),e)),n.addErrorInterceptor(async e=>(o.debugError("请求错误:",e),r.index.showToast({title:e.message,icon:"none",duration:2e3}),e)),exports.httpClient=n;
