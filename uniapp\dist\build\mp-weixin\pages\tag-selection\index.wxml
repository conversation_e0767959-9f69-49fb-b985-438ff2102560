<view class="tag-selection-container data-v-c6b26492"><view class="page-header data-v-c6b26492"><text class="page-title data-v-c6b26492">选择需要挑战的考点</text><text class="page-subtitle data-v-c6b26492">选择你想挑战的数学考点类型</text></view><view wx:if="{{a}}" class="loading-container data-v-c6b26492"><view class="loading-spinner data-v-c6b26492"></view><text class="loading-text data-v-c6b26492">正在加载标签...</text></view><view wx:elif="{{b}}" class="error-container data-v-c6b26492"><view class="error-icon data-v-c6b26492">⚠️</view><text class="error-title data-v-c6b26492">加载失败</text><text class="error-text data-v-c6b26492">{{c}}</text><button class="retry-btn data-v-c6b26492" bindtap="{{d}}"><text class="retry-text data-v-c6b26492">重试</text></button></view><view wx:elif="{{e}}" class="empty-container data-v-c6b26492"><view class="empty-icon data-v-c6b26492">🏷️</view><text class="empty-title data-v-c6b26492">暂无标签</text><text class="empty-text data-v-c6b26492">当前没有可用的标签挑战</text></view><view wx:else class="tags-list data-v-c6b26492"><view class="list-header data-v-c6b26492"><text class="list-title data-v-c6b26492">共{{f}}个标签</text></view><view class="tags-grid data-v-c6b26492"><view wx:for="{{g}}" wx:for-item="tag" wx:key="i" class="{{['tag-card', 'data-v-c6b26492', tag.j && 'tag-vip', tag.k && 'tag-inactive']}}" bindtap="{{tag.l}}"><view class="tag-icon data-v-c6b26492"><text class="icon-text data-v-c6b26492">{{tag.a}}</text><text wx:if="{{tag.b}}" class="vip-crown data-v-c6b26492">👑</text></view><view class="tag-info data-v-c6b26492"><text class="tag-name data-v-c6b26492">{{tag.c}}</text><text class="tag-desc data-v-c6b26492">{{tag.d}}</text><view class="tag-stats data-v-c6b26492"><view class="stat-item data-v-c6b26492"><text class="stat-label data-v-c6b26492">关卡数</text><text class="stat-value data-v-c6b26492">{{tag.e}}</text></view><view class="stat-item data-v-c6b26492"><text class="stat-label data-v-c6b26492">难度</text><text class="stat-value data-v-c6b26492">{{tag.f}}</text></view></view></view><view class="tag-status data-v-c6b26492"><view wx:if="{{tag.g}}" class="status-badge inactive data-v-c6b26492"><text class="status-text data-v-c6b26492">未激活</text></view><view wx:elif="{{tag.h}}" class="status-badge vip-only data-v-c6b26492"><text class="status-text data-v-c6b26492">VIP专享</text></view><view wx:else class="status-badge available data-v-c6b26492"><text class="status-text data-v-c6b26492">开始挑战</text></view></view></view></view></view><view class="bottom-actions data-v-c6b26492"><button class="back-btn data-v-c6b26492" bindtap="{{h}}"><text class="back-text data-v-c6b26492">返回</text></button></view></view>