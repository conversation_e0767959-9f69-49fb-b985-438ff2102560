<view class="level-selection-container data-v-3ce91d36"><view class="header-section data-v-3ce91d36"><view class="header-content data-v-3ce91d36"><text class="page-title data-v-3ce91d36">选择关卡</text><text class="page-subtitle data-v-3ce91d36">选择你想要挑战的关卡开始游戏吧！</text></view><view wx:if="{{a}}" class="progress-card data-v-3ce91d36"><view class="progress-item data-v-3ce91d36"><text class="progress-label data-v-3ce91d36">已解锁</text><text class="progress-value data-v-3ce91d36">{{b}}</text></view><view class="progress-item data-v-3ce91d36"><text class="progress-label data-v-3ce91d36">已完成</text><text class="progress-value data-v-3ce91d36">{{c}}</text></view><view class="progress-item data-v-3ce91d36"><text class="progress-label data-v-3ce91d36">总关卡</text><text class="progress-value data-v-3ce91d36">{{d}}</text></view></view></view><view wx:if="{{e}}" class="loading-container data-v-3ce91d36"><view class="loading-spinner data-v-3ce91d36"></view><text class="loading-text data-v-3ce91d36">正在加载关卡...</text></view><view wx:elif="{{f}}" class="error-container data-v-3ce91d36"><view class="error-icon data-v-3ce91d36">⚠️</view><text class="error-title data-v-3ce91d36">加载失败</text><text class="error-text data-v-3ce91d36">{{g}}</text><button class="retry-btn data-v-3ce91d36" bindtap="{{h}}"><text class="retry-text data-v-3ce91d36">重试</text></button></view><view wx:else class="levels-container data-v-3ce91d36"><view class="levels-grid data-v-3ce91d36"><view wx:for="{{i}}" wx:for-item="level" wx:key="k" class="{{['level-card', 'data-v-3ce91d36', level.l && 'level-locked', level.m && 'level-completed']}}" bindtap="{{level.n}}"><view class="level-main-content data-v-3ce91d36"><view class="level-number data-v-3ce91d36">{{level.a}}</view><view class="level-info data-v-3ce91d36"><text class="level-name data-v-3ce91d36">{{level.b}}</text><text class="level-desc data-v-3ce91d36">{{level.c}}</text></view><view class="level-status data-v-3ce91d36"><view wx:if="{{level.d}}" class="status-badge locked data-v-3ce91d36"><text class="status-text data-v-3ce91d36">🔒 未解锁</text></view><view wx:elif="{{level.e}}" class="status-badge completed data-v-3ce91d36"><view class="level-stars data-v-3ce91d36"><text wx:for="{{level.f}}" wx:for-item="star" wx:key="a" class="star star-filled data-v-3ce91d36"> ⭐ </text></view></view><view wx:else class="status-badge available data-v-3ce91d36"><text class="status-text data-v-3ce91d36">开始</text></view></view></view><view wx:if="{{level.g}}" class="level-tags data-v-3ce91d36"><view wx:for="{{level.h}}" wx:for-item="tag" wx:key="c" class="{{['tag-item', 'data-v-3ce91d36', tag.d && 'tag-vip']}}" style="{{'background-color:' + tag.e}}"><text class="tag-text data-v-3ce91d36">{{tag.a}}</text><text wx:if="{{tag.b}}" class="tag-vip-icon data-v-3ce91d36">👑</text></view><view wx:if="{{level.i}}" class="tag-more data-v-3ce91d36"><text class="tag-more-text data-v-3ce91d36">+{{level.j}}</text></view></view></view></view></view></view>