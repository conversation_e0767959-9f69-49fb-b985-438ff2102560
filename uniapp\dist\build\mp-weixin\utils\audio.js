"use strict";var o=Object.defineProperty,e=(e,t,s)=>(((e,t,s)=>{t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s})(e,"symbol"!=typeof t?t+"":t,s),s);const t=require("../common/vendor.js");let s=null,n="",i=!1,c=!1,r="";const a={main:"/static/audio/background-main.mp3",game:"/static/audio/background-game.mp3",menu:"/static/audio/background-menu.mp3"},l={click:"/static/audio/click.mp3",success:"/static/audio/success.mp3",fail:"/static/audio/fail.mp3",unlock:"/static/audio/unlock.mp3",complete:"/static/audio/complete.mp3"},u={10001:"系统错误",10002:"网络错误",10003:"文件错误",10004:"格式错误","-1":"未知错误","-99":"音频实例冲突错误"},g=new Map,d=class o{constructor(){e(this,"settings",{backgroundMusic:!0,soundEffects:!0,vibration:!0}),this.loadSettings()}static getInstance(){return o.instance||(o.instance=new o),o.instance}loadSettings(){try{const o=t.index.getStorageSync("gameSettings");o&&(this.settings={...this.settings,...JSON.parse(o)}),console.log("音频设置加载成功:",this.settings)}catch(o){console.error("加载音频设置失败:",o)}}saveSettings(){try{t.index.setStorageSync("gameSettings",JSON.stringify(this.settings)),console.log("音频设置保存成功:",this.settings)}catch(o){console.error("保存音频设置失败:",o)}}getSettings(){return{...this.settings}}updateSettings(o){this.settings={...this.settings,...o},this.saveSettings(),!this.settings.backgroundMusic&&i&&this.stopBackgroundMusic(),console.log("音频设置已更新:",this.settings)}createInnerAudioContext(o){return s&&n===o||(this.destroyInnerAudioContext(),s=t.index.createInnerAudioContext(),n=o,s.src=o,s.loop=!0,s.autoplay=!1,s.volume=.5,s.obeyMuteSwitch=!0,this.setupAudioEventListeners(s),console.log("创建内部音频上下文:",o)),s}setupAudioEventListeners(o){o.onCanplay(()=>{console.log("音频可以播放:",n)}),o.onPlay(()=>{i=!0,c=!1,console.log("背景音乐开始播放:",n)}),o.onPause(()=>{c=!0,console.log("背景音乐暂停:",n)}),o.onStop(()=>{i=!1,c=!1,console.log("背景音乐停止:",n)}),o.onEnded(()=>{i=!1,c=!1,console.log("背景音乐播放结束:",n)}),o.onError(o=>{const e=o.errCode||o.code||"unknown",t=u[e]||"未知错误";console.error("背景音乐播放错误:",{code:e,message:t,src:n,error:o}),i=!1,c=!1,-99===e&&(console.warn("检测到音频实例冲突，尝试重新创建"),setTimeout(()=>{this.destroyInnerAudioContext()},100))}),o.onWaiting(()=>{console.log("音频加载中:",n)}),o.onTimeUpdate(()=>{})}destroyInnerAudioContext(){if(s){try{s.stop(),s.destroy(),console.log("销毁音频上下文:",n)}catch(o){console.error("销毁音频上下文失败:",o)}s=null,n="",i=!1,c=!1,r=""}}playBackgroundMusic(o="main",e){if(this.settings.backgroundMusic)try{const t=e||a[o]||a.main;if(i&&n===t)return void console.log("相同背景音乐正在播放中:",o);if(c&&n===t&&s)return s.play(),void(r=o);this.createInnerAudioContext(t).play(),r=o,console.log("开始播放背景音乐:",o,t)}catch(t){console.error("播放背景音乐失败:",t)}else console.log("背景音乐已关闭，跳过播放")}pauseBackgroundMusic(){s&&i&&(s.pause(),console.log("背景音乐已暂停"))}resumeBackgroundMusic(){s&&c&&this.settings.backgroundMusic&&(s.play(),console.log("背景音乐已恢复"))}stopBackgroundMusic(){s&&(s.stop(),console.log("背景音乐已停止"))}playSoundEffect(o){if(this.settings.soundEffects)try{const e=l[o];let s=g.get(o);s||(s=t.index.createInnerAudioContext(),s.src=e,s.volume=.6,s.loop=!1,s.obeyMuteSwitch=!0,s.onPlay(()=>{console.log("音效播放:",o)}),s.onError(e=>{console.error("音效播放失败:",o,e),g.delete(o)}),s.onEnded(()=>{console.log("音效播放结束:",o)}),g.set(o,s)),s.play()}catch(e){console.error("播放音效失败:",o,e)}else console.log("音效已关闭，跳过播放")}vibrate(o="short"){if(this.settings.vibration)try{"short"===o?t.index.vibrateShort({success:()=>{console.log("短震动触发成功")},fail:o=>{console.error("短震动触发失败:",o)}}):t.index.vibrateLong({success:()=>{console.log("长震动触发成功")},fail:o=>{console.error("长震动触发失败:",o)}})}catch(e){console.error("震动触发失败:",e)}else console.log("震动已关闭，跳过震动")}onPageShow(){this.settings.backgroundMusic&&c&&this.resumeBackgroundMusic()}onPageHide(){i&&this.pauseBackgroundMusic()}destroy(){this.destroyInnerAudioContext(),g.forEach((o,e)=>{try{o.destroy()}catch(t){console.error("销毁音效实例失败:",e,t)}}),g.clear(),console.log("音频资源已销毁")}getPlayStatus(){return{isPlaying:i,isPaused:c,currentSrc:n,musicType:r}}};e(d,"instance");const h=d.getInstance();exports.audioManager=h;
