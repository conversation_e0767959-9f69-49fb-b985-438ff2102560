/**
 * 环境配置工具
 * 统一管理不同环境下的配置
 */

import { BUILD_ENVIRONMENT } from "../config/build-env";

// 环境类型
export type Environment = "development" | "staging" | "production";

// 环境配置接口
export interface EnvConfig {
  apiBaseUrl: string;
  apiPrefix: string;
  debug: boolean;
  environment: Environment;
}

/**
 * 获取当前环境
 * 优先使用构建时配置，然后使用运行时检测
 */
export function getCurrentEnvironment(): Environment {
  // 方案1: 优先使用构建时配置
  if (BUILD_ENVIRONMENT === "production") {
    return "production";
  }
  if (BUILD_ENVIRONMENT === "development") {
    return "development";
  }
  if (BUILD_ENVIRONMENT === "staging") {
    return "staging";
  }

  // 方案2: 运行时检测（备用方案）
  // #ifdef MP-WEIXIN
  // 微信小程序：检查运行环境版本
  try {
    const accountInfo = uni.getAccountInfoSync();
    if (accountInfo && accountInfo.miniProgram) {
      const envVersion = accountInfo.miniProgram.envVersion;
      // release: 正式版, trial: 体验版, develop: 开发版
      if (envVersion === "release") {
        return "production";
      }
      return "development";
    }
  } catch (error) {
    // 获取失败时继续其他检测
  }
  // #endif

  // #ifdef H5
  // H5环境：检查URL
  if (typeof location !== "undefined") {
    const hostname = location.hostname;
    // 生产环境域名
    if (hostname.includes("quhu.work")) {
      return "production";
    }
    // 本地开发环境
    if (
      hostname === "localhost" ||
      hostname === "127.0.0.1" ||
      hostname.includes("dev")
    ) {
      return "development";
    }
  }
  // #endif

  // #ifdef APP-PLUS
  // App环境：检查调试模式
  try {
    // @ts-ignore
    if (typeof plus !== "undefined" && plus.runtime) {
      // @ts-ignore
      return plus.runtime.isDebugMode ? "development" : "production";
    }
  } catch (error) {
    // 忽略错误
  }
  // #endif

  // 默认开发环境
  return "development";
}

/**
 * 获取环境配置
 */
export function getEnvConfig(): EnvConfig {
  const env = getCurrentEnvironment();

  // 从环境变量读取配置，如果没有则使用默认值
  const getEnvVar = (key: string, defaultValue: string): string => {
    // #ifdef H5
    // H5环境下从 import.meta.env 读取
    if (typeof import.meta !== "undefined" && import.meta.env) {
      // @ts-ignore
      return import.meta.env[key] || defaultValue;
    }
    // #endif

    // 其他环境使用默认值
    return defaultValue;
  };

  // 基础配置 - 优先从环境变量读取
  const configs: Record<Environment, EnvConfig> = {
    development: {
      apiBaseUrl: getEnvVar("VITE_API_BASE_URL", "http://localhost:18891"),
      apiPrefix: getEnvVar("VITE_API_PREFIX", "/api/v1/weixin"),
      debug: getEnvVar("VITE_DEBUG", "true") === "true",
      environment: "development",
    },
    staging: {
      apiBaseUrl: getEnvVar("VITE_API_BASE_URL", "http://*************:18891"),
      apiPrefix: getEnvVar("VITE_API_PREFIX", "/api/v1/weixin"),
      debug: getEnvVar("VITE_DEBUG", "true") === "true",
      environment: "staging",
    },
    production: {
      apiBaseUrl: getEnvVar("VITE_API_BASE_URL", "https://api.quhu.work"),
      apiPrefix: getEnvVar("VITE_API_PREFIX", "/api/v1/weixin"),
      debug: getEnvVar("VITE_DEBUG", "false") === "true",
      environment: "production",
    },
  };

  return configs[env];
}

/**
 * 是否为开发环境（严格模式，只有 development 才算开发环境）
 */
export function isDevelopment(): boolean {
  const env = getCurrentEnvironment();
  return env === "development";
}

/**
 * 是否为生产环境
 */
export function isProduction(): boolean {
  return getCurrentEnvironment() === "production";
}

/**
 * 是否启用调试模式
 */
export function isDebugMode(): boolean {
  return getEnvConfig().debug;
}

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  return getEnvConfig().apiBaseUrl;
}

/**
 * 获取完整的API URL
 */
export function getApiUrl(path: string): string {
  const config = getEnvConfig();
  const baseUrl = config.apiBaseUrl;
  const prefix = config.apiPrefix;

  // 确保路径以 / 开头
  const normalizedPath = path.startsWith("/") ? path : `/${path}`;

  return `${baseUrl}${prefix}${normalizedPath}`;
}

/**
 * 记录环境信息（只在开发环境下输出）
 */
export function logEnvironmentInfo(): void {
  // 只在开发环境下输出环境信息
  if (getCurrentEnvironment() !== "development") {
    return;
  }

  const config = getEnvConfig();

  console.log("🌍 环境信息:");
  console.log(`  环境: ${config.environment}`);
  console.log(`  API地址: ${config.apiBaseUrl}`);
  console.log(`  API前缀: ${config.apiPrefix}`);
  console.log(`  调试模式: ${config.debug}`);

  // #ifdef H5
  if (typeof location !== "undefined") {
    console.log(`  当前域名: ${location.hostname}`);
    console.log(`  当前端口: ${location.port}`);
  }
  // #endif
}
