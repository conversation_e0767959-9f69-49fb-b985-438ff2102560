"use strict";const e=require("../../common/vendor.js"),o=e.defineComponent({__name:"index",setup(o){const l=e.ref(""),a=e.ref(""),t=e.ref(!0),u=e.ref("");e.onMounted(()=>{const o=getCurrentPages(),n=o[o.length-1].options||{};return console.log("WebView页面参数:",n),n.url&&(l.value=decodeURIComponent(n.url)),n.title&&(a.value=decodeURIComponent(n.title),e.index.setNavigationBarTitle({title:a.value})),l.value?v(l.value)?void console.log("准备加载URL:",l.value):(u.value="无效的URL格式",void(t.value=!1)):(u.value="缺少URL参数",void(t.value=!1))});const v=e=>{try{const o=new URL(e);return"http:"===o.protocol||"https:"===o.protocol}catch{return!1}},n=e=>{console.log("WebView加载完成:",e),t.value=!1,u.value=""},r=e=>{console.error("WebView加载错误:",e),t.value=!1,u.value="页面加载失败，请检查网络连接"},c=e=>{console.log("收到WebView消息:",e)},i=()=>{l.value?(t.value=!0,u.value="",setTimeout(()=>{t.value&&(t.value=!1,u.value="加载超时，请重试")},1e4)):u.value="缺少URL参数"},s=()=>{e.index.navigateBack({delta:1})};return(o,a)=>e.e({a:t.value},(t.value,{}),{b:u.value},u.value?{c:e.t(u.value),d:e.o(i),e:e.o(s)}:{},{f:!t.value&&!u.value&&l.value},t.value||u.value||!l.value?{}:{g:l.value,h:e.o(c),i:e.o(n),j:e.o(r)})}}),l=e._export_sfc(o,[["__scopeId","data-v-a2a7c15e"]]);wx.createPage(l);
