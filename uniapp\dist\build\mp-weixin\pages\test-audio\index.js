"use strict";const a=require("../../common/vendor.js"),e=require("../../utils/audio.js"),u=require("../../composables/useGlobalConfig.js"),n=a.defineComponent({__name:"index",setup(n){const o=a.ref({isPlaying:!1,isPaused:!1,currentSrc:"",musicType:""}),i=a.ref({backgroundMusic:!0,soundEffects:!0,vibration:!0}),{getBackgroundMusicUrl:t,initializeGlobalConfig:s}=u.useGlobalConfig(),c=a.ref({main:"",game:"",menu:""});let r=null;a.onMounted(async()=>{await s(),d(),l(),g()}),a.onUnmounted(()=>{r&&clearInterval(r)});const d=()=>{i.value=e.audioManager.getSettings()},l=()=>{c.value={main:t("main"),game:t("game"),menu:t("menu")}},g=()=>{r=setInterval(()=>{o.value=e.audioManager.getPlayStatus()},500)},M=()=>{e.audioManager.playBackgroundMusic("main")},m=()=>{e.audioManager.playBackgroundMusic("game")},v=()=>{e.audioManager.playBackgroundMusic("menu")},f=()=>{e.audioManager.pauseBackgroundMusic()},p=()=>{e.audioManager.resumeBackgroundMusic()},y=()=>{e.audioManager.stopBackgroundMusic()},k=()=>{e.audioManager.playSoundEffect("click")},b=()=>{e.audioManager.playSoundEffect("success")},S=()=>{e.audioManager.playSoundEffect("fail")},B=()=>{e.audioManager.playSoundEffect("unlock")},E=()=>{e.audioManager.playSoundEffect("complete")},x=()=>{e.audioManager.vibrate("short")},P=()=>{e.audioManager.vibrate("long")},_=a=>{const u=a.detail.value;e.audioManager.updateSettings({backgroundMusic:u}),d()},w=a=>{const u=a.detail.value;e.audioManager.updateSettings({soundEffects:u}),d()},C=a=>{const u=a.detail.value;e.audioManager.updateSettings({vibration:u}),d()},j=()=>{const a=t("main");e.audioManager.playBackgroundMusic("main",a)},q=async()=>{await s(),l(),a.index.showToast({title:"配置已刷新",icon:"success"})},h=()=>{a.index.navigateBack({delta:1})};return(e,u)=>({a:a.t(o.value.isPlaying?"是":"否"),b:a.t(o.value.isPaused?"是":"否"),c:a.t(o.value.musicType||"无"),d:a.t(o.value.currentSrc||"无"),e:a.o(M),f:a.o(m),g:a.o(v),h:a.o(f),i:a.o(p),j:a.o(y),k:a.o(k),l:a.o(b),m:a.o(S),n:a.o(B),o:a.o(E),p:a.o(x),q:a.o(P),r:i.value.backgroundMusic,s:a.o(_),t:i.value.soundEffects,v:a.o(w),w:i.value.vibration,x:a.o(C),y:a.t(c.value.main),z:a.t(c.value.game),A:a.t(c.value.menu),B:a.o(j),C:a.o(q),D:a.o(h)})}}),o=a._export_sfc(n,[["__scopeId","data-v-e752d929"]]);wx.createPage(o);
