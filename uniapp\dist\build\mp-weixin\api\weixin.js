"use strict";var e=Object.defineProperty,t=(t,o,n)=>(((t,o,n)=>{o in t?e(t,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[o]=n})(t,"symbol"!=typeof o?o+"":o,n),n);const o=require("../common/vendor.js"),n=require("./config.js"),r=require("../utils/env.js"),i=require("./request.js"),s=require("./utils.js");let c="";const l=new class{constructor(){t(this,"baseUrl"),this.baseUrl=r.getApiBaseUrl()}setOpenid(e){c=e,o.index.setStorageSync(n.STORAGE_KEYS.USER_OPENID,e),console.log("设置 openid:",e)}getOpenid(){if(!c||""===c){const e=o.index.getStorageSync(n.STORAGE_KEYS.USER_OPENID);c=e||""}return c}initializeOpenid(){const e=o.index.getStorageSync(n.STORAGE_KEYS.USER_OPENID);e&&(c=e,console.log("从本地存储恢复 openid:",e))}async bindUser(e){try{const t=n.getWeixinApiUrl("/user/bind"),r={openid:this.getOpenid(),phone:(null==e?void 0:e.phone)||"",nickname:(null==e?void 0:e.nickname)||"微信用户",avatarUrl:(null==e?void 0:e.avatarUrl)||""},s=await i.httpClient.post(t,r);return o.index.setStorageSync(n.STORAGE_KEYS.USER_INFO,JSON.stringify(s)),this.setOpenid(r.openid),console.log("用户绑定成功:",s),s}catch(t){throw console.error("用户绑定失败:",t),t}}async getUserInfo(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const r=n.getWeixinApiUrl(`/user/info?openid=${t}`),s=await i.httpClient.get(r);return o.index.setStorageSync(n.STORAGE_KEYS.USER_INFO,JSON.stringify(s)),console.log("获取用户信息成功:",s),s}catch(t){throw console.error("获取用户信息失败:",t),t}}async getUserInfoById(e){try{const t=n.getWeixinApiUrl(`/user/${e}/info`),o=await i.httpClient.get(t);return console.log("根据ID获取用户信息成功:",o),o}catch(t){throw console.error("根据ID获取用户信息失败:",t),t}}async getLevels(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const o=n.getWeixinApiUrl(`/levels?openid=${t}`),r=await i.httpClient.get(o);return console.log("获取关卡列表成功:",r),r}catch(t){throw console.error("获取关卡列表失败:",t),t}}async forceRefreshWeixinSession(){return new Promise(e=>{o.index.checkSession({success:()=>{console.log("检测到旧的微信会话，准备清除..."),e()},fail:()=>{console.log("没有旧的微信会话"),e()}})})}async weixinLogin(){return await this.forceRefreshWeixinSession(),new Promise((e,t)=>{console.log("开始获取新的微信登录code..."),o.index.login({provider:"weixin",timeout:1e4,success:o=>{console.log("微信登录成功:",o),o.code?(console.log("获取到新的微信code:",o.code),e({code:o.code})):t(new Error("未获取到登录凭证"))},fail:e=>{console.error("微信登录失败:",e),t(new Error(`微信登录失败: ${e.errMsg||"未知错误"}`))}})})}async performWeixinLogin(e){try{console.log("开始微信登录流程，清除旧的登录状态..."),this.clearLocalUserData();const t=await this.weixinLogin();console.log("获取微信登录凭证成功:",t.code);const r={code:t.code,nickname:(null==e?void 0:e.nickname)||"微信用户",avatarUrl:(null==e?void 0:e.avatarUrl)||""};(null==e?void 0:e.phone)&&(r.phone=e.phone),console.log("发送登录请求:",r);const s=n.getWeixinApiUrl("/login"),c=await i.httpClient.post(s,r);return"success"===c.status?(o.index.setStorageSync(n.STORAGE_KEYS.USER_INFO,JSON.stringify(c.userInfo)),this.setOpenid(c.openid),console.log("微信登录成功:",c)):"need_bind"===c.status&&(this.setOpenid(c.openid),console.log("需要绑定手机号:",c)),c}catch(t){console.error("微信登录流程失败:",t);const e=t instanceof Error?t.message:String(t);if(e.includes("code已经被使用")||e.includes("code been used")||e.includes("40163"))throw console.log("检测到code重复使用错误，清除本地状态..."),this.clearLocalUserData(),new Error("登录状态已过期，请重新尝试");throw t}}async bindPhone(e){try{const t=n.getWeixinApiUrl("/bind-phone"),r=await i.httpClient.post(t,e);if("success"===r.status)return o.index.setStorageSync(n.STORAGE_KEYS.USER_INFO,JSON.stringify(r.userInfo)),this.setOpenid(r.openid),console.log("手机号绑定成功:",r.userInfo),r.userInfo;throw new Error("绑定失败")}catch(t){throw console.error("绑定手机号失败:",t),t}}async checkWeixinConfig(){try{const e=n.getWeixinApiUrl("/config"),t=await i.httpClient.get(e);return console.log("微信配置状态:",t),t}catch(e){throw console.error("检查微信配置失败:",e),e}}async checkSession(){return new Promise(e=>{o.index.checkSession({success:()=>{console.log("登录状态有效"),e(!0)},fail:()=>{console.log("登录状态已过期"),e(!1)}})})}async getShareConfig(e){try{const t=n.getWeixinApiUrl("/share-config"),o=e?s.appendQueryToUrl(t,{page:e.page,levelId:e.levelId,userId:e.userId}):t,r=await i.httpClient.get(o);return console.log("获取分享配置成功:",r),r.default}catch(t){console.error("获取分享配置失败:",t);const e={title:"趣护消消乐 - 挑战你的词汇量！",path:"/pages/index/index",imageUrl:"/static/share-logo.png",desc:"快来挑战趣护消消乐，提升你的词汇量！",summary:"快来挑战趣护消消乐，提升你的词汇量！"};return console.log("使用默认分享配置:",e),e}}async getDailyStatus(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const o=`${n.getWeixinApiUrl("/daily-status")}?openid=${t}`,r=await i.httpClient.get(o);return console.log("获取每日状态成功:",r),r}catch(t){throw console.error("获取每日状态失败:",t),t}}async shareForReward(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const o=n.getWeixinApiUrl("/share"),r=await i.httpClient.post(o,{openid:t});return console.log("分享奖励获取成功:",r),r}catch(t){throw console.error("分享奖励获取失败:",t),t}}async getShareReward(){try{const e=this.getOpenid();if(!e)throw new Error("缺少 openid 参数");const t=n.getWeixinApiUrl("/share"),r=await i.httpClient.post(t,{openid:e});return console.log("分享奖励获取成功:",r),r.userInfo&&o.index.setStorageSync(n.STORAGE_KEYS.USER_INFO,JSON.stringify(r.userInfo)),r}catch(e){throw console.error("获取分享奖励失败:",e),e}}async checkUserBound(e){try{return await this.getUserInfo(e),!0}catch(t){if(404===t.statusCode)return!1;throw t}}getLocalUserInfo(){try{const e=o.index.getStorageSync(n.STORAGE_KEYS.USER_INFO);return e?JSON.parse(e):null}catch(e){return console.error("获取本地用户信息失败:",e),null}}getLocalOpenid(){try{return this.getOpenid()||null}catch(e){return console.error("获取本地openid失败:",e),null}}clearLocalUserData(){try{o.index.removeStorageSync(n.STORAGE_KEYS.USER_INFO),o.index.removeStorageSync(n.STORAGE_KEYS.USER_OPENID),c="",console.log("清除本地用户数据成功")}catch(e){console.error("清除本地用户数据失败:",e)}}async refreshUserInfo(){try{return await this.getUserInfo()}catch(e){return console.error("刷新用户信息失败:",e),null}}async getLevelDetail(e,t){try{const o=t||this.getOpenid();if(!o)throw new Error("缺少 openid 参数");const r=n.getWeixinApiUrl(`/level/${e}?openid=${o}`),s=await i.httpClient.get(r);return console.log("获取关卡详情成功:",s),s}catch(o){throw console.error("获取关卡详情失败:",o),o}}async completeLevel(e,t,o){try{const e=this.getOpenid();if(!e)throw new Error("缺少 openid 参数");const r=n.getWeixinApiUrl("/level/complete"),s={levelId:t,openid:e,completionTimeSeconds:o},c=await i.httpClient.post(r,s);return console.log("关卡完成记录成功:",c),c}catch(r){throw console.error("记录关卡完成失败:",r),r}}async completeLevelLegacy(e,t,r=60){try{const i=await this.completeLevel(e,t,r),s={id:i.userId||e,maskedPhone:"",unlockedLevels:i.unlockedLevels,completedLevelIds:[],totalGames:0,totalCompletions:i.totalCompletions,lastPlayTime:(new Date).toISOString(),createdAt:"",isVip:i.isVip,dailyUnlockLimit:i.dailyUnlockLimit,dailyUnlockCount:i.dailyUnlockCount,dailyShared:!1,lastPlayDate:(new Date).toISOString().split("T")[0],totalShares:0};return o.index.setStorageSync(n.STORAGE_KEYS.USER_INFO,JSON.stringify(s)),s}catch(i){throw console.error("记录关卡完成失败:",i),i}}async getVipPackages(){try{const e=n.getWeixinApiUrl("/vip-packages"),t=await i.httpClient.get(e);return console.log("获取VIP套餐列表成功:",t),t}catch(e){throw console.error("获取VIP套餐列表失败:",e),e}}async createPayment(e){try{const t=n.getWeixinApiUrl("/create-payment"),o=await i.httpClient.post(t,e);return console.log("创建支付订单成功:",o),o}catch(t){throw console.error("创建支付订单失败:",t),t}}async getPaymentStatus(e){try{const t=n.getWeixinApiUrl(`/payment-status/${e}`),o=await i.httpClient.get(t);return console.log("查询支付状态成功:",o),o}catch(t){throw console.error("查询支付状态失败:",t),t}}async getPaymentOrders(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const o=`${n.getWeixinApiUrl("/payment-orders")}?openid=${t}}`,r=await i.httpClient.get(o);return console.log("获取支付订单列表成功:",r),r}catch(t){throw console.error("获取支付订单列表失败:",t),t}}async requestPayment(e){try{const t=this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const n=await this.createPayment({openid:t,packageId:e});return console.log("支付参数:",n),new Promise((e,t)=>{o.index.requestPayment({provider:"wxpay",orderInfo:{timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.package,signType:n.signType,paySign:n.paySign},success:t=>{console.log("支付成功:",t),o.index.showToast({title:"支付成功",icon:"success",duration:2e3}),e(!0)},fail:e=>{console.error("支付失败:",e),e.errMsg&&e.errMsg.includes("cancel")?o.index.showToast({title:"支付已取消",icon:"none",duration:2e3}):o.index.showToast({title:"支付失败",icon:"none",duration:2e3}),t(e)}})})}catch(t){throw console.error("发起支付失败:",t),o.index.showToast({title:"支付失败",icon:"none",duration:2e3}),t}}async getGlobalConfig(){try{const e=n.getWeixinApiUrl("/global-config"),t=await i.httpClient.get(e);if(console.log(t,"response"),t)return t;throw new Error("获取全局配置失败")}catch(e){console.error("获取全局配置失败:",e);const t={backgroundMusicUrl:"",helpUrl:"",app:{name:"趣护消消乐",version:"1.0.0",description:"护理知识游戏化 无痛提高知识水平"}};return console.log("使用默认全局配置:",t),t}}async getExtendedLevels(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const o=n.getWeixinApiUrl(`/levels?openid=${t}`),r=await i.httpClient.get(o);return console.log("获取扩展关卡列表成功:",r),r}catch(t){console.error("获取扩展关卡列表失败:",t);try{console.log("尝试使用传统关卡列表接口");const t=await this.getLevels(e);return t.map(e=>({...e,tagIds:[],userStars:0,bestTime:void 0,timeLimit:void 0,completionCount:0,lastPlayTime:void 0}))}catch(o){throw console.error("传统关卡列表接口也失败:",o),t}}}async submitGameResult(e){try{const t=await this.completeLevel(e.openid,e.levelId,e.completionTime),o={success:t.isSuccess,message:t.message,stars:t.stars,isNewRecord:!0,userStats:{totalStars:0,threeStarLevels:0,twoStarLevels:0,oneStarLevels:0,completedLevels:t.totalCompletions||0},hasUnlockedNewLevel:t.hasUnlockedNewLevel||!1,unlockedLevels:t.unlockedLevels||0};return console.log("使用传统通关API成功:",o),o}catch(t){throw console.error("传统通关API也失败:",t),t}}async getUserFavorites(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const o=`${this.baseUrl}/api/v1/user-favorites?openid=${t}`,n=await i.httpClient.get(o);return console.log("获取用户收藏列表成功:",n),n}catch(t){throw console.error("获取用户收藏列表失败:",t),t}}async getTags(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const o=`${this.baseUrl}/api/v1/tags?openid=${t}`,n=await i.httpClient.get(o);return console.log("获取标签列表成功:",n),n}catch(t){throw console.error("获取标签列表失败:",t),t}}async getActiveTags(){try{const e=`${this.baseUrl}/api/v1/tags/active`,t=await i.httpClient.get(e);return console.log("获取激活标签列表成功:",t),t}catch(e){throw console.error("获取激活标签列表失败:",e),e}}async getTagLevels(e,t){try{const o=t||this.getOpenid();let n=`${this.baseUrl}/api/v1/tags/${e}/levels`;o&&(n+=`?openid=${o}`);const r=await i.httpClient.get(n);return console.log("获取标签关卡列表成功:",r),r}catch(o){throw console.error("获取标签关卡列表失败:",o),o}}async getUserStarStats(e){try{const t=e||this.getOpenid();if(!t)throw new Error("缺少 openid 参数");const o=`${this.baseUrl}/api/v1/user-stars/stats?openid=${t}`,n=await i.httpClient.get(o);return console.log("获取用户星级统计成功:",n),n}catch(t){throw console.error("获取用户星级统计失败:",t),t}}async updateLevelStars(e,t,o){try{const r=o||this.getOpenid();if(!r)throw new Error("缺少 openid 参数");const s={stars:t.stars||1,completionTime:1e3*t.completionTime,timeLimit:t.timeLimit?1e3*t.timeLimit:void 0},c=n.getWeixinApiUrl(`/user-stars/levels/${e}?openid=${r}`),l=await i.httpClient.post(c,s);return console.log("更新关卡星级成功:",l),l}catch(r){throw console.error("更新关卡星级失败:",r),r}}async redeemActivationCode(e,t){try{const o=t||this.getOpenid();if(!o)throw new Error("缺少 openid 参数");const n=`${this.baseUrl}/api/v1/activation-codes/redeem`,r={code:e,openid:o},s=await i.httpClient.post(n,r);return console.log("兑换激活码成功:",s),s}catch(o){throw console.error("兑换激活码失败:",o),o}}async addFavorite(e,t){try{const o=t||this.getOpenid();if(!o)throw new Error("缺少 openid 参数");const n=`${this.baseUrl}/api/v1/user-favorites?openid=${o}`,r={levelId:e},s=await i.httpClient.post(n,r);return console.log("添加收藏成功:",s),s}catch(o){throw console.error("添加收藏失败:",o),o}}async removeFavorite(e,t){try{const o=t||this.getOpenid();if(!o)throw new Error("缺少 openid 参数");const n=`${this.baseUrl}/api/v1/user-favorites/${e}?openid=${o}`,r=await i.httpClient.delete(n);return console.log("移除收藏成功:",r),r}catch(o){throw console.error("移除收藏失败:",o),o}}};l.initializeOpenid(),exports.weixinApi=l;
