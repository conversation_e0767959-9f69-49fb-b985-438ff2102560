"use strict";const e=require("../../common/vendor.js"),n=require("../../api/weixin.js"),i=require("../../utils/audio.js"),t=require("../../api/utils.js"),s=require("../../utils/auth.js"),l=e.defineComponent({__name:"index",setup(l){const o=e.reactive({level:[],currentLevel:null,...t.createLoadingState()}),c=e.ref(null),a=e.ref(null);e.ref(null);const r=e.ref(null),d=e.computed(()=>o.level.map((e,n)=>{const i=e;return{id:e.id,levelNumber:String(n+1).padStart(2,"0"),name:e.name,description:e.description,library:m(e),locked:!e.isUnlocked,completed:e.isCompleted,difficulty:e.difficulty,maxStars:i.userStars||0,tags:i.tags||[],isFavorited:i.isFavorited||!1}}));e.computed(()=>d.value.some(e=>e.locked));const g=[{name:"基础词汇",words:[{english:"apple",chinese:"苹果"},{english:"book",chinese:"书"},{english:"cat",chinese:"猫"},{english:"dog",chinese:"狗"},{english:"egg",chinese:"鸡蛋"},{english:"fish",chinese:"鱼"},{english:"girl",chinese:"女孩"},{english:"hat",chinese:"帽子"}]},{name:"动物世界",words:[{english:"elephant",chinese:"大象"},{english:"tiger",chinese:"老虎"},{english:"lion",chinese:"狮子"},{english:"monkey",chinese:"猴子"},{english:"rabbit",chinese:"兔子"},{english:"bird",chinese:"鸟"},{english:"horse",chinese:"马"},{english:"sheep",chinese:"羊"}]},{name:"颜色彩虹",words:[{english:"red",chinese:"红色"},{english:"blue",chinese:"蓝色"},{english:"green",chinese:"绿色"},{english:"yellow",chinese:"黄色"},{english:"purple",chinese:"紫色"},{english:"orange",chinese:"橙色"},{english:"black",chinese:"黑色"},{english:"white",chinese:"白色"}]},{name:"数字王国",words:[{english:"one",chinese:"一"},{english:"two",chinese:"二"},{english:"three",chinese:"三"},{english:"four",chinese:"四"},{english:"five",chinese:"五"},{english:"six",chinese:"六"},{english:"seven",chinese:"七"},{english:"eight",chinese:"八"}]}];e.onLoad(async e=>{await s.checkLoginAndRedirect({toastMessage:"请先登录以选择关卡",redirectUrl:"/pages/login/index?redirect="+encodeURIComponent("/pages/level-selection/index")})&&(null==e?void 0:e.scrollToLevel)&&(r.value=e.scrollToLevel,console.log("需要滚动到关卡:",r.value))}),e.onMounted(async()=>{await h(),r.value&&(await e.nextTick$1(),w(r.value))});const h=async()=>{await t.withLoading(o,async()=>{await u(),await f(),await v()},{errorMessage:"页面加载失败，请重试"})},u=async()=>{try{const e=await n.weixinApi.getUserInfo();c.value=e,console.log("用户信息加载成功:",e)}catch(e){console.error("加载用户信息失败:",e)}},f=async()=>{try{try{const e=await n.weixinApi.getExtendedLevels();return o.level=e,void console.log("扩展关卡列表加载成功:",e)}catch(e){console.warn("扩展关卡API不可用，使用基础API:",e)}const i=await n.weixinApi.getLevels();o.level=i,console.log("基础关卡列表加载成功:",i)}catch(i){console.error("加载关卡列表失败:",i),console.log("使用本地备用关卡数据"),o.level=p()}},v=async()=>{try{const e=await n.weixinApi.getDailyStatus();a.value=e,console.log("每日状态加载成功:",e)}catch(e){console.error("加载每日状态失败:",e)}},p=()=>[{id:"fallback-level-1",name:"第1关 - 基础词汇",difficulty:1,description:"小学基础词汇练习",isUnlocked:!0,isCompleted:!1,createdAt:(new Date).toISOString()},{id:"fallback-level-2",name:"第2关 - 动物世界",difficulty:2,description:"认识可爱的动物",isUnlocked:!0,isCompleted:!1,createdAt:(new Date).toISOString()},{id:"fallback-level-3",name:"第3关 - 颜色彩虹",difficulty:3,description:"学习各种颜色",isUnlocked:!1,isCompleted:!1,createdAt:(new Date).toISOString()},{id:"fallback-level-4",name:"第4关 - 数字王国",difficulty:4,description:"掌握数字表达",isUnlocked:!1,isCompleted:!1,createdAt:(new Date).toISOString()}],m=e=>{const n=(e.difficulty-1)%g.length;return g[n]},w=n=>{try{console.log("开始滚动到关卡:",n);const i=d.value.findIndex(e=>e.id.toString()===n.toString());if(-1===i)return void console.warn("未找到目标关卡:",n);console.log("找到目标关卡索引:",i);const t=220*i;e.index.pageScrollTo({scrollTop:t,duration:800,success:()=>{console.log("滚动到关卡成功:",n),setTimeout(()=>{e.index.showToast({title:`已定位到第${i+1}关`,icon:"none",duration:1500})},500)},fail:e=>{console.error("滚动到关卡失败:",e)}})}catch(i){console.error("滚动到关卡出错:",i)}},y=async n=>{try{await v();const n=a.value;if(!n)return void e.index.showToast({title:"该关卡尚未解锁",icon:"none",duration:1500});if(n.isVip)return void e.index.showToast({title:"该关卡尚未解锁",icon:"none",duration:1500});n.remainingUnlocks>0?e.index.showModal({title:"解锁关卡",content:`是否使用1次解锁机会来解锁这个关卡？\n剩余解锁次数：${n.remainingUnlocks}`,confirmText:"解锁",cancelText:"取消",success:async n=>{n.confirm&&(e.index.showToast({title:"关卡解锁成功！",icon:"success"}),await f())}}):e.index.showModal({title:"解锁次数不足",content:"今日解锁次数已用完\n• 升级VIP可无限解锁\n• 分享游戏可获得额外解锁机会",confirmText:"升级VIP",cancelText:"分享获取",success:n=>{n.confirm?x():e.index.showToast({title:"请分享游戏给好友",icon:"none"})}})}catch(i){console.error("检查解锁状态失败:",i),e.index.showToast({title:"该关卡尚未解锁",icon:"none",duration:1500})}},x=()=>{e.index.showToast({title:"VIP功能开发中",icon:"none"})};return(n,t)=>{var s;return e.e({a:c.value},c.value?{b:e.t(c.value.unlockedLevels||0),c:e.t((null==(s=c.value.completedLevelIds)?void 0:s.length)||0),d:e.t(d.value.length)}:{},{e:o.isLoading},o.isLoading?{}:o.error?{g:e.t(o.error),h:e.o(f)}:{i:e.f(d.value,(n,t,s)=>e.e({a:e.t(n.levelNumber),b:e.t(n.name),c:e.t(n.description),d:n.locked},n.locked?{}:n.completed?{f:e.f(n.maxStars||0,(e,n,i)=>({a:e}))}:{},{e:n.completed,g:n.tags&&n.tags.length>0},n.tags&&n.tags.length>0?e.e({h:e.f(n.tags.slice(0,3),(n,i,t)=>e.e({a:e.t(n.name),b:n.isVip},(n.isVip,{}),{c:n.id,d:n.isVip?1:"",e:n.color||"#f0f0f0"})),i:n.tags.length>3},n.tags.length>3?{j:e.t(n.tags.length-3)}:{}):{},{k:n.id,l:n.locked?1:"",m:n.completed?1:"",n:e.o(t=>(async n=>{if(n.locked)return i.audioManager.playSoundEffect("fail"),void(await y());i.audioManager.playSoundEffect("click"),console.log("Selected level:",n),e.index.setStorageSync("selectedLibrary",JSON.stringify(n.library)),e.index.setStorageSync("selectedLevel",JSON.stringify(n)),e.index.navigateTo({url:"/pages/game/index"})})(n),n.id)}))},{f:o.error})}}}),o=e._export_sfc(l,[["__scopeId","data-v-3ce91d36"]]);wx.createPage(o);
