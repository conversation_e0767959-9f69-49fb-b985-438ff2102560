"use strict";const e=require("../../common/vendor.js"),a=require("../../api/weixin.js"),i=require("../../utils/audio.js"),t=require("../../utils/auth.js"),o=e.defineComponent({__name:"index",setup(o){const l=e.ref(""),n=e.ref(!1),d=e.ref(null),r=e.ref(null),c=e.ref([]),u=e.computed(()=>c.value.filter(e=>e.isCompleted).length),s=e.computed(()=>c.value.map((e,a)=>({...e,levelNumber:String(a+1).padStart(2,"0"),locked:!e.isUnlocked,completed:e.isCompleted,isFavorited:e.isFavorited||!1})));e.onLoad(async e=>{await t.checkLoginAndRedirect({toastMessage:"请先登录以查看标签关卡",redirectUrl:"/pages/login/index"})&&((null==e?void 0:e.tagId)?(l.value=e.tagId,await v()):d.value="缺少标签ID参数")});const v=async()=>{if(l.value)try{n.value=!0,d.value=null;const e=(await a.weixinApi.getActiveTags()).find(e=>e.id===l.value);e&&(r.value=e);const i=await a.weixinApi.getTagLevels(l.value);c.value=i.levels,console.log("标签关卡列表加载成功:",i)}catch(e){console.error("加载标签关卡列表失败:",e),d.value="加载失败，请重试"}finally{n.value=!1}else d.value="缺少标签ID参数"};return(t,o)=>{var l,f;return e.e({a:e.t((null==(l=r.value)?void 0:l.name)||"标签关卡"),b:e.t((null==(f=r.value)?void 0:f.description)||"挑战该标签下的所有关卡"),c:e.t(c.value.length),d:e.t(u.value),e:n.value},n.value?{}:d.value?{g:e.t(d.value),h:e.o(v)}:{i:e.f(s.value,(t,o,l)=>e.e({a:e.t(t.levelNumber),b:e.t(t.name),c:e.t(t.description),d:t.locked},t.locked?{}:t.completed?{f:e.f(t.userStars||0,(e,a,i)=>({a:e}))}:{},{e:t.completed,g:e.t(t.isFavorited?"💖":"🤍"),h:t.isFavorited?1:"",i:e.o(o=>(async t=>{try{i.audioManager.playSoundEffect("click"),t.isFavorited?(await a.weixinApi.removeFavorite(t.id),t.isFavorited=!1,e.index.showToast({title:"已取消收藏",icon:"success",duration:1500})):(await a.weixinApi.addFavorite(t.id),t.isFavorited=!0,e.index.showToast({title:"已添加收藏",icon:"success",duration:1500})),i.audioManager.playSoundEffect("complete")}catch(o){console.error("收藏操作失败:",o),i.audioManager.playSoundEffect("fail"),e.index.showToast({title:"操作失败，请重试",icon:"none",duration:1500})}})(t),t.id),j:t.id,k:t.locked?1:"",l:t.completed?1:"",m:e.o(a=>(async a=>{if(a.locked)return i.audioManager.playSoundEffect("fail"),void e.index.showToast({title:"该关卡尚未解锁",icon:"none",duration:1500});i.audioManager.playSoundEffect("click"),console.log("Selected level:",a),e.index.setStorageSync("selectedLevel",JSON.stringify(a)),e.index.navigateTo({url:"/pages/game/index"})})(t),t.id)}))},{f:d.value})}}}),l=e._export_sfc(o,[["__scopeId","data-v-b488fc06"]]);wx.createPage(l);
