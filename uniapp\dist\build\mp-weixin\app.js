"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const o=require("./common/vendor.js"),e=require("./api/weixin.js"),n=require("./utils/env.js");Math;const s=o.defineComponent({__name:"App",setup(n){const s=o.reactive({userInfo:null,isLoggedIn:!1,isLoading:!1,error:null});o.onLaunch(async()=>{console.log("App Launch"),await r()}),o.onShow(()=>{console.log("App Show"),i()}),o.onHide(()=>{console.log("App Hide")});const r=async()=>{try{s.isLoading=!0,s.error=null;const n=e.weixinApi.getLocalUserInfo(),r=e.weixinApi.getOpenid();if(n&&r&&"openid"!==r){s.userInfo=n,s.isLoggedIn=!0,console.log("从本地存储恢复用户信息:",n),console.log("当前使用的 openid:",r);try{const o=await e.weixinApi.getUserInfo();s.userInfo=o,console.log("用户信息已更新:",o)}catch(o){console.warn("刷新用户信息失败，使用本地缓存:",o)}}else console.log("未找到本地用户信息，用户需要手动登录"),console.log("当前 openid:",r),s.isLoggedIn=!1,r&&"openid"!==r&&(console.log("清除旧的openid，避免登录冲突"),e.weixinApi.clearLocalUserData())}catch(o){console.error("应用初始化失败:",o),s.error="应用初始化失败"}finally{s.isLoading=!1}},i=async()=>{try{const o=await e.weixinApi.refreshUserInfo();o&&(s.userInfo=o,console.log("用户信息已刷新:",o))}catch(o){console.warn("刷新用户信息失败:",o)}},l=()=>{e.weixinApi.clearLocalUserData(),s.userInfo=null,s.isLoggedIn=!1,s.error=null,console.log("用户已登出"),o.index.showToast({title:"已退出登录",icon:"success",duration:1500})},t=getApp();return t&&(t.globalData={...t.globalData,userState:s,refreshUserInfo:i,logout:l}),()=>{}}});function r(){const e=o.createSSRApp(s);return n.logEnvironmentInfo(),{app:e}}r().app.mount("#app"),exports.createApp=r;
