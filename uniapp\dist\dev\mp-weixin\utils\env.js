"use strict";
require("../common/vendor.js");
function getCurrentEnvironment() {
  {
    return "staging";
  }
}
function getEnvConfig() {
  const env = getCurrentEnvironment();
  const getEnvVar = (key, defaultValue) => {
    return defaultValue;
  };
  const configs = {
    development: {
      apiBaseUrl: getEnvVar("VITE_API_BASE_URL", "http://localhost:18891"),
      apiPrefix: getEnvVar("VITE_API_PREFIX", "/api/v1/weixin"),
      debug: getEnvVar("VITE_DEBUG", "true") === "true",
      environment: "development"
    },
    staging: {
      apiBaseUrl: getEnvVar("VITE_API_BASE_URL", "http://*************:18891"),
      apiPrefix: getEnvVar("VITE_API_PREFIX", "/api/v1/weixin"),
      debug: getEnvVar("VITE_DEBUG", "true") === "true",
      environment: "staging"
    },
    production: {
      apiBaseUrl: getEnvVar("VITE_API_BASE_URL", "https://api.quhu.work"),
      apiPrefix: getEnvVar("VITE_API_PREFIX", "/api/v1/weixin"),
      debug: getEnvVar("VITE_DEBUG", "false") === "true",
      environment: "production"
    }
  };
  return configs[env];
}
function getApiBaseUrl() {
  return getEnvConfig().apiBaseUrl;
}
function getApiUrl(path) {
  const config = getEnvConfig();
  const baseUrl = config.apiBaseUrl;
  const prefix = config.apiPrefix;
  const normalizedPath = path.startsWith("/") ? path : `/${path}`;
  return `${baseUrl}${prefix}${normalizedPath}`;
}
function logEnvironmentInfo() {
  if (getCurrentEnvironment() !== "development") {
    return;
  }
  const config = getEnvConfig();
  console.log("🌍 环境信息:");
  console.log(`  环境: ${config.environment}`);
  console.log(`  API地址: ${config.apiBaseUrl}`);
  console.log(`  API前缀: ${config.apiPrefix}`);
  console.log(`  调试模式: ${config.debug}`);
}
exports.getApiBaseUrl = getApiBaseUrl;
exports.getApiUrl = getApiUrl;
exports.getCurrentEnvironment = getCurrentEnvironment;
exports.logEnvironmentInfo = logEnvironmentInfo;
