"use strict";const e=require("../../common/vendor.js");require("../../api/request.js");const a=require("../../api/weixin.js"),i=require("../../utils/share.js"),t=require("../../utils/debug-control.js"),n=e.defineComponent({__name:"index",setup(n){const s=e.ref(!1),l=e.ref(""),u=e.ref(""),r=e.ref(""),o=e.ref("level-uuid-123"),c=e.ref("12345678"),v=e.ref("等待API调用...");e.onMounted(()=>{d(),s.value&&m()});const d=()=>{try{s.value=t.shouldShowDebugPages();const e=t.getDebugEnvironmentInfo();l.value=e.environment,s.value||t.debugLog("调试页面访问被拒绝，当前环境:"+e.environment)}catch(e){t.debugError("检查调试权限失败:",e),s.value=!1}},g=()=>{e.index.navigateBack({fail:()=>{e.index.switchTab({url:"/pages/index/index"})}})},m=()=>{u.value=a.weixinApi.getOpenid()},p=()=>{r.value.trim()&&(a.weixinApi.setOpenid(r.value.trim()),m(),r.value="",e.index.showToast({title:"OpenID 已设置",icon:"success"}))},y=async()=>{try{v.value="正在获取用户信息...";const e=await a.weixinApi.getUserInfo();v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},h=async()=>{try{v.value="正在获取关卡列表...";const e=await a.weixinApi.getlevel();v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},w=async()=>{try{v.value="正在获取微信登录凭证...";const e=await a.weixinApi.weixinLogin();v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},x=async()=>{try{v.value="正在检查登录状态...";const e=await a.weixinApi.checkSession();v.value=JSON.stringify({isValid:e,message:e?"登录状态有效":"登录状态已过期"},null,2)}catch(e){v.value=`错误: ${e.message}`}},f=async()=>{try{v.value="正在执行完整微信登录流程（只使用uni.login）...";const e=await a.weixinApi.performWeixinLogin({phone:"",nickname:"测试用户",avatarUrl:""});v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},I=async()=>{try{v.value="正在绑定手机号...";const e=await a.weixinApi.bindPhone({openid:a.weixinApi.getOpenid(),phone:"",nickname:"测试用户",avatarUrl:"https://via.placeholder.com/100x100"});v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},S=async()=>{try{v.value="正在检查微信配置...";const e=await a.weixinApi.checkWeixinConfig();v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},$=async()=>{try{if(!o.value.trim())return void(v.value="请输入关卡ID");v.value="正在获取关卡详情...";const e=await a.weixinApi.getLevelDetail(o.value.trim());v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},k=async()=>{try{if(!c.value.trim()||!o.value.trim())return void(v.value="请输入用户ID和关卡ID");v.value="正在调用通关接口...";const e=await a.weixinApi.completeLevel(c.value.trim(),o.value.trim());v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},A=async()=>{try{if(!c.value.trim()||!o.value.trim())return void(v.value="请输入用户ID和关卡ID");v.value="正在调用开始游戏接口...";const e=await a.weixinApi.startGame(c.value.trim(),o.value.trim());v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},D=async()=>{try{v.value="正在获取分享配置...";const e=await a.weixinApi.getShareConfig({page:"pages/index/index",levelId:o.value.trim()||void 0,userId:c.value.trim()||void 0});v.value=JSON.stringify(e,null,2)}catch(e){v.value=`错误: ${e.message}`}},O=async()=>{try{v.value="正在测试微信分享...",await i.shareUtils.shareToWeixin({scene:"WXSceneSession",type:0,params:{page:"pages/index/index",levelId:o.value.trim()||void 0,userId:c.value.trim()||void 0}}),v.value="微信分享调用成功（请查看微信分享界面）"}catch(e){v.value=`错误: ${e.message}`}},P=async()=>{try{v.value="正在测试系统分享...",await i.shareUtils.shareWithSystem({page:"pages/index/index",levelId:o.value.trim()||void 0,userId:c.value.trim()||void 0}),v.value="系统分享调用成功（请查看系统分享界面）"}catch(e){v.value=`错误: ${e.message}`}},J=async()=>{try{if(!c.value.trim())return void(v.value="请输入用户ID");v.value="正在获取分享奖励...";const i=await a.weixinApi.getShareReward({userId:c.value.trim(),shareType:"app_message",page:"pages/index/index",levelId:o.value.trim()||void 0,timestamp:Date.now()});v.value=JSON.stringify(i,null,2),i.success&&e.index.showToast({title:`获得${i.reward.description}！`,icon:"success",duration:3e3})}catch(i){v.value=`错误: ${i.message}`}},N=async()=>{try{if(!c.value.trim())return void(v.value="请输入用户ID");const a=i.shareUtils.checkDailyShareReward(c.value.trim()),t=(new Date).toDateString();v.value=JSON.stringify({userId:c.value.trim(),date:t,hasSharedToday:a,message:a?"今日已获取过分享奖励":"今日尚未获取分享奖励"},null,2),e.index.showToast({title:a?"今日已分享":"今日未分享",icon:a?"success":"none",duration:2e3})}catch(a){v.value=`错误: ${a.message}`}},T=async()=>{try{if(!c.value.trim())return void(v.value="请输入用户ID");const a=(new Date).toDateString(),i=`daily_share_reward_${c.value.trim()}_${a}`;e.index.removeStorageSync(i),v.value=JSON.stringify({userId:c.value.trim(),date:a,action:"已重置今日分享状态",storageKey:i},null,2),e.index.showToast({title:"分享状态已重置",icon:"success",duration:2e3})}catch(a){v.value=`错误: ${a.message}`}},U=async()=>{try{if(!c.value.trim())return void(v.value="请输入用户ID");v.value="正在测试防重复执行机制...\n";const a=[];for(let e=0;e<5;e++)a.push(i.shareUtils.claimShareReward({userId:c.value.trim(),shareType:"app_message",page:"pages/debug/index",timestamp:Date.now()+e}));await Promise.all(a),v.value+="\n测试完成：已连续发起5次分享奖励请求\n",v.value+="如果防重复机制正常工作，应该只有第一次请求被处理\n",v.value+='其他请求应该被跳过并显示"正在处理中"的日志',e.index.showToast({title:"防重复测试完成",icon:"success",duration:2e3})}catch(a){v.value=`错误: ${a.message}`}},V=async()=>{try{if(!c.value.trim())return void(v.value="请输入用户ID");v.value="正在获取每日状态...\n";const i=await a.weixinApi.getDailyStatus();v.value=JSON.stringify({action:"获取每日状态",result:i,analysis:{isVip:i.isVip?"VIP用户，无限制":"普通用户",unlockStatus:`${i.dailyUnlockCount}/${i.dailyUnlockLimit}`,remaining:i.remainingUnlocks,canUnlock:i.canUnlock,shareStatus:i.dailyShared?"今日已分享":"今日未分享"}},null,2),e.index.showToast({title:"每日状态获取成功",icon:"success",duration:2e3})}catch(i){v.value=`错误: ${i.message}`}},_=async()=>{try{if(!c.value.trim())return void(v.value="请输入用户ID");v.value="正在测试新分享API...\n";const i=await a.weixinApi.shareForReward();v.value=JSON.stringify({action:"新分享API测试",result:i,analysis:{status:i.status,message:i.message,unlockInfo:"success"===i.status?{dailyUnlockCount:i.dailyUnlockCount,dailyUnlockLimit:i.dailyUnlockLimit,remainingUnlocks:i.remainingUnlocks,isVip:i.isVip,totalShares:i.totalShares}:null}},null,2),e.index.showToast({title:"新分享API测试完成",icon:"success",duration:2e3})}catch(i){v.value=`错误: ${i.message}`}},b=async()=>{try{v.value="正在获取VIP套餐列表...\n";const i=await a.weixinApi.getVipPackages();v.value=JSON.stringify({action:"获取VIP套餐列表",result:i,analysis:{packageCount:i.length,packages:i.map(e=>({id:e.id,name:e.name,price:`¥${(e.price/100).toFixed(2)}`,duration:`${e.duration}天`,isActive:e.isActive}))}},null,2),e.index.showToast({title:"VIP套餐获取成功",icon:"success",duration:2e3})}catch(i){v.value=`错误: ${i.message}`}},C=async()=>{try{if(!c.value.trim())return void(v.value="请输入用户ID");v.value="正在测试VIP支付...\n";const i=await a.weixinApi.getVipPackages();if(0===i.length)return void(v.value="没有可用的VIP套餐");const t=i[0];v.value+=`选择测试套餐: ${t.name}\n`,v.value+=`价格: ¥${(t.price/100).toFixed(2)}\n`,v.value+=`时长: ${t.duration}天\n\n`;const n=await a.weixinApi.createPayment({openid:c.value.trim(),packageId:t.id});v.value+="支付参数创建成功:\n",v.value+=JSON.stringify({action:"创建VIP支付订单",packageInfo:{id:t.id,name:t.name,price:t.price,duration:t.duration},paymentParams:{appId:n.appId,timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.package,signType:n.signType,paySign:n.paySign?"已生成":"未生成"}},null,2),e.index.showToast({title:"VIP支付测试完成",icon:"success",duration:2e3})}catch(i){v.value=`错误: ${i.message}`}},L=()=>{e.index.navigateTo({url:"/pages/test-icons/index"})},q=()=>{e.index.navigateTo({url:"/pages/test-audio/index"})};return(a,i)=>e.e({a:!s.value},s.value?{c:e.t(l.value),d:e.t(u.value),e:e.o(m),f:r.value,g:e.o(e=>r.value=e.detail.value),h:e.o(p),i:e.o(w),j:e.o(x),k:e.o(f),l:e.o(I),m:e.o(S),n:e.o(y),o:e.o(h),p:e.o($),q:e.o(k),r:e.o(A),s:e.o(D),t:e.o(O),v:e.o(P),w:e.o(J),x:e.o(N),y:e.o(T),z:e.o(U),A:e.o(V),B:e.o(_),C:e.o(b),D:e.o(C),E:e.o(L),F:e.o(q),G:o.value,H:e.o(e=>o.value=e.detail.value),I:c.value,J:e.o(e=>c.value=e.detail.value),K:e.t(v.value)}:{b:e.o(g)})}}),s=e._export_sfc(n,[["__scopeId","data-v-21878f15"]]);wx.createPage(s);
