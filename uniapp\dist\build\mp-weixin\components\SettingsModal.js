"use strict";const e=require("../common/vendor.js"),o=require("../utils/audio.js"),a=e.defineComponent({__name:"SettingsModal",props:{visible:{type:Boolean}},emits:["close","settingsChange"],setup(a,{emit:t}){const n=a,i=t,s=e.reactive({backgroundMusic:!0,soundEffects:!0,vibration:!0}),c=()=>{const e=o.audioManager.getSettings();Object.assign(s,e)};e.watch(()=>n.visible,e=>{e&&c()});const u=e=>{const a=e.detail.value;s.backgroundMusic=a,o.audioManager.updateSettings({backgroundMusic:a}),a?o.audioManager.playBackgroundMusic("main"):o.audioManager.stopBackgroundMusic(),i("settingsChange",{...s}),console.log("背景音乐设置已更改:",a)},d=e=>{const a=e.detail.value;s.soundEffects=a,o.audioManager.updateSettings({soundEffects:a}),i("settingsChange",{...s}),console.log("音效设置已更改:",a)},r=e=>{const a=e.detail.value;s.vibration=a,o.audioManager.updateSettings({vibration:a}),a&&o.audioManager.vibrate("short"),i("settingsChange",{...s}),console.log("震动设置已更改:",a)},g=()=>{o.audioManager.playSoundEffect("click"),setTimeout(()=>{o.audioManager.playSoundEffect("success")},500),o.audioManager.vibrate("short"),e.index.showToast({title:"音效测试完成",icon:"none",duration:1500})},l=()=>{i("close")},M=()=>{l()};return c(),(o,a)=>e.e({a:o.visible},o.visible?{b:e.o(l),c:s.backgroundMusic,d:e.o(u),e:s.soundEffects,f:e.o(d),g:s.vibration,h:e.o(r),i:e.o(g),j:e.o(l),k:e.o(()=>{}),l:e.o(M)}:{})}}),t=e._export_sfc(a,[["__scopeId","data-v-3f2d524c"]]);wx.createComponent(t);
