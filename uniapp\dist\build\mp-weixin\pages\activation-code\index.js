"use strict";const e=require("../../common/vendor.js"),a=require("../../api/weixin.js"),u=require("../../utils/audio.js"),s=require("../../utils/auth.js"),c=e.defineComponent({__name:"index",setup(c){const l=e.ref(""),t=e.ref(!1),i=e.ref(!1),n=e.ref({success:!1,message:""}),o=e.computed(()=>l.value.trim().length>0&&!t.value);e.onLoad(async()=>{await s.checkLoginAndRedirect({toastMessage:"请先登录以兑换激活码",redirectUrl:"/pages/login/index"})});const r=async()=>{if(!o.value)return;const s=l.value.trim();if(s)try{t.value=!0,u.audioManager.playSoundEffect("click");const e=await a.weixinApi.redeemActivationCode(s);n.value=e,i.value=!0,e.success?(u.audioManager.playSoundEffect("complete"),u.audioManager.vibrate("short"),l.value=""):u.audioManager.playSoundEffect("fail")}catch(c){console.error("兑换激活码失败:",c),u.audioManager.playSoundEffect("fail"),n.value={success:!1,message:"兑换失败，请检查网络连接或稍后重试"},i.value=!0}finally{t.value=!1}else e.index.showToast({title:"请输入激活码",icon:"none"})},v=()=>{i.value=!1,n.value.success},d=()=>{u.audioManager.playSoundEffect("click"),e.index.navigateBack()};return(a,u)=>e.e({a:t.value,b:l.value,c:e.o(e=>l.value=e.detail.value),d:e.t(t.value?"兑换中...":"立即兑换"),e:o.value?"":1,f:!o.value,g:e.o(r),h:i.value},i.value?e.e({i:e.t(n.value.success?"🎉":"❌"),j:e.t(n.value.success?"兑换成功！":"兑换失败"),k:e.t(n.value.message),l:n.value.success&&n.value.package},n.value.success&&n.value.package?{m:e.t(n.value.package.name),n:e.t(n.value.package.description)}:{},{o:e.o(v)}):{},{p:e.o(d)})}}),l=e._export_sfc(c,[["__scopeId","data-v-86a49c34"]]);wx.createPage(l);
