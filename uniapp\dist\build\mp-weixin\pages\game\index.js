"use strict";const e=require("../../common/vendor.js"),o=require("../../api/weixin.js"),l=require("../../api/utils.js"),a=require("../../utils/share.js"),t=require("../../utils/audio.js"),n=require("../../composables/useGlobalConfig.js"),r=require("../../utils/mockData.js");Math||s();const s=()=>"../../components/SettingsModal.js",i=e.defineComponent({__name:"index",setup(s,{expose:i}){const c=e.ref(null),u=e.ref(null),d=e.ref(1),g=e.ref(null),h=e.ref(null),v=e.ref(!1),p=e.ref(null);let w=!1;const f=e.ref([]),m=e.ref([]),y=e.ref(0),$=e.ref(0),S=e.ref(!1),x=e.ref(null),M=e.ref(null),k=e.ref(null),I=e.ref(60),L=e.ref(0),b=e.ref(0),H=e.ref(!1),z=e.ref(!1);let T=0;const C=()=>{const e=["#FFE1E6","#E1F0FF","#E1FFE1"];return e[Math.floor(Math.random()*e.length)]},E=e.ref(!1),W=e.ref(""),D=e.ref(!1),A=e.ref(!0),_=e.ref(!1),F=e.ref(!1),G=e.ref(!1),P=e.ref(!1),j=e.ref(0),B=e.ref(""),O=e.ref(!1),q=e.ref(r.isH5Environment()),R=e.ref(q.value),U=e.ref(!1),N=e.ref({backgroundMusic:!0,soundEffects:!0,vibration:!0}),{getBackgroundMusicUrl:J,initializeGlobalConfig:X}=n.useGlobalConfig(),Y=e.computed(()=>{if(g.value)return!0;if(!c.value||!c.value.words)return!1;const e=Math.min(Math.floor(c.value.words.length/8),1e3);return d.value<e}),K=e.computed(()=>{if(console.log("🔍 计算当前关卡词汇..."),console.log("  - currentLevelDetail.value:",g.value),console.log("  - selectedLibraryInfo.value:",c.value),console.log("  - currentLevelId.value:",d.value),console.log("  - useMockData.value:",R.value),g.value&&g.value.phrases){console.log("📚 使用关卡详情中的词组数据:",g.value.phrases.length,"个词汇");const e=g.value.phrases.map(e=>({english:e.text||e.english,chinese:e.meaning||e.chinese,id:e.id,pronunciation:e.pronunciation,category:e.category}));return console.log("✅ 关卡词汇处理完成:",e.length,"个词汇"),console.log("📝 词汇示例:",e.slice(0,2)),e}if(c.value&&c.value.words){console.log("📖 使用词库中的词汇数据:",c.value.words.length,"个词汇");const e=c.value.words,o=8*(d.value-1)%e.length,l=[];for(let a=0;a<8;a++){const t=(o+a)%e.length;l.push(e[t])}return console.log("✅ 词库词汇处理完成:",l.length,"个词汇"),l}return console.warn("⚠️ 没有可用的词汇数据"),console.log("📊 当前状态:"),console.log("  - currentLevelDetail.value:",g.value),console.log("  - selectedLibraryInfo.value:",c.value),[]}),Q=e=>{const o=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`},V=()=>{x.value=Date.now(),I.value=60,H.value=!1,k.value&&clearInterval(k.value),k.value=setInterval(()=>{if(x.value&&I.value>0){const e=Math.floor((Date.now()-x.value)/1e3);I.value=Math.max(0,60-e),I.value<=0&&ee()}},1e3)},Z=()=>{k.value&&(clearInterval(k.value),k.value=null),x.value&&(M.value=Date.now(),L.value=Math.floor((M.value-x.value)/1e3))},ee=()=>{console.log("⏰ 时间到期！游戏结束"),H.value=!0,Z(),L.value=60,b.value=0,t.audioManager.playSoundEffect("fail"),t.audioManager.vibrate("short"),setTimeout(()=>{be(!1)},1e3)};e.onLoad(async()=>{try{if(q.value){console.log("🌐 检测到H5环境，启用Mock数据模式");const o={id:r.mockLibraries[0].id,name:r.mockLibraries[0].name,description:r.mockLibraries[0].description,words:r.getMockWordsForLevel(1,16)},l={id:r.mockLevels[0].id,name:r.mockLevels[0].name,libraryId:r.mockLibraries[0].id};console.log("📦 设置Mock存储数据:"),console.log("  - mockLibraryData:",o),console.log("  - mockLevelData:",l),e.index.setStorageSync("selectedLibrary",JSON.stringify(o)),e.index.setStorageSync("selectedLevel",JSON.stringify(l)),console.log("✅ H5环境Mock数据初始化完成");const a=e.index.getStorageSync("selectedLibrary"),t=e.index.getStorageSync("selectedLevel");console.log("🔍 验证存储数据:"),console.log("  - storedLibrary:",a),console.log("  - storedLevel:",t)}await X(),await le(),oe()}catch(o){console.error("❌ onLoad初始化失败:",o)}});const oe=()=>{try{const e=t.audioManager.getSettings();if(N.value={...e},console.log("音频设置初始化完成:",e),e.backgroundMusic){const e=J("game");console.log("播放游戏页面背景音乐:",e),t.audioManager.playBackgroundMusic("game",e)}}catch(e){console.error("初始化音频设置失败:",e)}};e.onShow(()=>{console.log("游戏页面显示"),t.audioManager.onPageShow();if(t.audioManager.getSettings().backgroundMusic){const e=J("game");t.audioManager.playBackgroundMusic("game",e)}}),e.onHide(()=>{console.log("游戏页面隐藏"),t.audioManager.onPageHide()});const le=async()=>{try{console.log("🎮 开始游戏初始化..."),A.value=!0,_.value=!1,F.value=!1,G.value=!1,P.value=!1,console.log("📱 加载用户信息..."),await re(),console.log("🎯 加载关卡信息...");const l=e.index.getStorageSync("selectedLevel");let a=!1;if(l)try{p.value=JSON.parse(l),console.log("获取到选择的关卡数据:",p.value),await ae(p.value.id),h.value&&await te(p.value.id),a=!0}catch(o){console.error("Failed to parse selected level data:",o),console.log("🔄 尝试使用词库数据作为备用..."),await ne(),a=!0}else console.log("🔄 没有关卡数据，使用词库数据作为备用..."),await ne(),a=!0;if(console.log("🔍 检查游戏数据完整性..."),console.log("  - gameDataLoaded:",a),console.log("  - wordsForCurrentLevel.length:",K.value.length),console.log("  - currentLevel:",u.value),console.log("  - currentLevelDetail:",g.value),!a||K.value.length<1)throw console.error("❌ 游戏数据不足:"),console.error("  - gameDataLoaded:",a),console.error("  - wordsForCurrentLevel.length:",K.value.length),console.error("  - 需要至少1个单词"),new Error(`无法获取足够的游戏数据: 需要至少1个单词，当前只有${K.value.length}个`);if(console.log("🧮 开始计算卡片位置..."),A.value=!1,_.value=!0,await new Promise(e=>setTimeout(e,100)),console.log("🎲 初始化游戏棋盘..."),await fe(),console.log("✅ 位置计算完成，准备渲染..."),_.value=!1,F.value=!0,await new Promise(e=>setTimeout(e,100)),console.log("🎨 开始最终渲染准备..."),!he())throw new Error("游戏数据未正确生成或不完整");console.log("🎮 开始加载游戏..."),F.value=!1,G.value=!0,j.value=0,B.value="正在启动游戏...",await ve(2e3),console.log("🎉 游戏加载完成，开始渲染"),G.value=!1,P.value=!0,console.log("⏱️ 开始游戏计时..."),V(),console.log("💖 检查关卡收藏状态..."),await $e(),console.log("✨ 游戏初始化完成，欢迎开始游戏！")}catch(a){console.error("❌ 游戏初始化失败:",a),A.value=!1,_.value=!1,F.value=!1,G.value=!1,P.value=!1,l.showError(`游戏初始化失败: ${a.message||"未知错误"}`)}},ae=async e=>{var l,a;try{if(console.log("正在加载关卡详情:",e),R.value){console.log("🔧 H5环境：使用mock关卡数据");const o=r.mockLevels.find(o=>o.id.toString()===e)||r.mockLevels[0],a=r.getMockWordsForLevel(parseInt(e),8),t={id:o.id,name:o.name,description:o.description,difficulty:o.difficulty,wordsCount:o.wordsCount,isUnlocked:!0,isCompleted:!1,createdAt:(new Date).toISOString(),phrases:a.map(e=>({id:e.id,text:e.english,meaning:e.chinese,createdAt:(new Date).toISOString()}))},n=await r.mockApiResponse(t,400);g.value=n.data,u.value={id:n.data.id,name:n.data.name,wordsCount:(null==(l=n.data.phrases)?void 0:l.length)||0},console.log("Mock关卡详情加载成功:",n.data)}else{const l=await o.weixinApi.getLevelDetail(e);g.value=l,u.value={id:l.id,name:l.name,wordsCount:(null==(a=l.phrases)?void 0:a.length)||0},console.log("关卡详情加载成功:",l)}}catch(t){throw console.error("加载关卡详情失败:",t),t}},te=async e=>{try{if(h.value&&h.value.id)if(R.value){console.log("🔧 H5环境：模拟记录游戏开始");const e=await r.mockApiResponse({success:!0,gameId:Date.now(),startTime:(new Date).toISOString()},200);console.log("Mock游戏开始记录成功:",e.data)}else await o.weixinApi.startGame(h.value.id,e),console.log("游戏开始记录成功")}catch(l){console.error("记录游戏开始失败:",l)}},ne=async()=>{if(console.log("🔄 开始加载词库数据作为备用..."),R.value){console.log("🔧 H5环境：使用mock词库数据");const e=r.mockLibraries[0],o=r.mockLevels[0];c.value={id:e.id,name:e.name,description:e.description,words:r.getMockWordsForLevel(1,16)},d.value=o.id,u.value={id:o.id,name:o.name,wordsCount:8},console.log("✅ 成功使用mock词库数据:",c.value),console.log("📚 当前关卡词汇数量:",K.value.length)}else{const l=e.index.getStorageSync("selectedLibrary");if(!l)throw console.error("❌ 没有找到词库数据"),new Error("未选择关卡或词库");try{c.value=JSON.parse(l);const e=se(c.value.id);if(d.value=e,u.value={id:d.value,name:`第${d.value}关`,wordsCount:8},console.log("✅ 成功使用词库数据作为备用:",c.value),console.log("📚 当前关卡词汇数量:",K.value.length),K.value.length<1)throw new Error(`词库数据不足: 需要至少1个词汇，当前只有${K.value.length}个`)}catch(o){throw console.error("❌ 加载词库数据失败:",o),new Error(`加载词库数据失败: ${o.message||"未知错误"}`)}}},re=async()=>{try{if(R.value){console.log("🔧 H5环境：使用mock用户数据");const e=await r.mockApiResponse(r.mockUser,300);h.value=e.data,console.log("Mock用户信息加载成功:",h.value)}else{const e=o.weixinApi.getLocalUserInfo();e&&(h.value=e,console.log("游戏页面获取到用户信息:",e))}}catch(e){if(console.error("加载用户信息失败:",e),!R.value){console.log("🔄 用户信息获取失败，降级使用mock数据");const e=await r.mockApiResponse(r.mockUser,100);h.value=e.data}}},se=o=>{const l=`currentLevel_${o}`,a=e.index.getStorageSync(l);return a?parseInt(a):1},ie=(e,o)=>{const l=Math.floor(97.5),a=Math.max(l,80)+15;return console.log(`固定间距计算: 卡片尺寸(160×35) = ${a}rpx`),a},ce=()=>{const e=160,o=35,l=Math.floor((e+o)/2),a=Math.max(l,80)+15;return console.log(`标准间距: ${a}rpx`),a},ue=(e,o,l=null)=>(null===l&&(l=ie()),((e,o,l=0)=>{const a=l+10,t=e.x-a,n=e.x+e.width+a,r=e.y-a,s=e.y+e.height+a,i=o.x-a,c=o.x+o.width+a,u=o.y-a,d=o.y+o.height+a,g=!(n<=i||t>=c||s<=u||r>=d);if(g){const l=e.x+e.width/2,t=e.y+e.height/2,n=o.x+o.width/2,r=o.y+o.height/2,s=Math.sqrt(Math.pow(l-n,2)+Math.pow(t-r,2)),i=195+a;if(s<i)return console.warn(`碰撞检测: 中心距离${Math.round(s)}rpx < 最小距离${Math.round(i)}rpx`),!0}return g})(e,o,l)),de=()=>{try{const o=e.index.getSystemInfoSync(),l=o.screenWidth||375,a=o.screenHeight||667,t=160,n=35,r=ce(),s=30,i=4,c=4,u=i*t+(i-1)*r+2*s,d=c*n+(c-1)*r+2*s,g=750-100;let h=Math.min(g,u);if(h<u){console.warn("CSS宽度限制过小，使用紧凑布局");const e=(()=>{const e=ce(),o=Math.floor(.6*e),l=Math.max(o,50);return console.log(`紧凑间距: ${l}rpx`),l})();h=Math.min(g,i*t+(i-1)*e+2*s)}const v=Math.floor(.7*a*(750/l));let p=Math.max(d,Math.floor(1.2*h));return p=Math.min(p,v),console.log("� 随机定位参数:"),console.log(`   - 卡片尺寸: ${t}×${n}rpx`),console.log("   - 总卡片数: 16张"),console.log(`   - 标准间距: ${r}rpx, 边距: ${s}rpx`),console.log(`   - 最小要求: ${u}×${d}rpx`),console.log(`   - CSS限制: ${g}rpx`),console.log(`   - 实际尺寸: ${h}×${p}rpx`),{containerWidth:h,containerHeight:p}}catch(o){return console.warn("获取屏幕信息失败，使用默认尺寸:",o),{containerWidth:700,containerHeight:900}}};class ge{constructor(e,o,l){this.containerWidth=e,this.containerHeight=o,this.cardCount=l,this.cardWidth=160,this.cardHeight=35,this.padding=20,this.calculateGrid(),this.occupiedGrids=new Set}calculateGrid(){const e=this.containerWidth-2*this.padding,o=this.containerHeight-2*this.padding;this.gridWidth=this.cardWidth+40,this.gridHeight=1.5*(this.cardHeight+40),this.cols=Math.floor(e/this.gridWidth),this.rows=Math.floor(o/this.gridHeight);const l=this.cols*this.rows;l<this.cardCount&&this.adjustGridSize(),console.log(`📐 网格系统: ${this.cols}列 × ${this.rows}行 = ${l}个网格，需要${this.cardCount}个`),console.log(`📏 网格尺寸: ${this.gridWidth} × ${this.gridHeight} (高度已调整为1.5倍)`)}adjustGridSize(){const e=this.containerWidth-2*this.padding,o=this.containerHeight-2*this.padding,l=Math.ceil(1.5*this.cardCount);let a=4,t=4,n=e/a,r=o/t;for(let s=3;s<=8;s++)for(let i=3;i<=8;i++)if(s*i>=l){const l=e/s,c=o/i;if(l>=this.cardWidth+20&&c>=1.5*(this.cardHeight+20)){a=s,t=i,n=l,r=c;break}}this.cols=a,this.rows=t,this.gridWidth=n,this.gridHeight=r,console.log(`🔧 调整后网格: ${this.cols}列 × ${this.rows}行，网格尺寸: ${Math.round(this.gridWidth)} × ${Math.round(this.gridHeight)}`)}getRandomAvailableGrid(){const e=this.cols*this.rows,o=[];for(let n=0;n<e;n++)this.occupiedGrids.has(n)||o.push(n);if(0===o.length)return console.warn("⚠️ 没有可用的网格位置"),null;const l=o[Math.floor(Math.random()*o.length)];this.occupiedGrids.add(l);const a=Math.floor(l/this.cols),t=l%this.cols;return{gridIndex:l,row:a,col:t,gridX:this.padding+t*this.gridWidth,gridY:this.padding+a*this.gridHeight,gridWidth:this.gridWidth,gridHeight:this.gridHeight}}getRandomPositionInGrid(e){const o=e.gridWidth-this.cardWidth,l=e.gridHeight-this.cardHeight,a=Math.max(0,o),t=Math.max(0,l),n=Math.random()*a,r=Math.random()*t,s=e.gridX+n,i=e.gridY+r;return console.log(`📍 网格(${e.row},${e.col}) -> 卡片位置(${Math.round(s)}, ${Math.round(i)})`),{x:Math.round(s),y:Math.round(i)}}reset(){this.occupiedGrids.clear(),console.log("🔄 网格系统已重置")}}const he=()=>{if(console.log("🔍 检查渲染准备状态..."),console.log("📊 基础数据检查:"),console.log("  - wordsForCurrentLevel.length:",K.value.length),console.log("  - currentLevel:",u.value),console.log("  - gameBoard.length:",f.value.length),K.value.length<1)return console.error("❌ 词汇数据不足，需要至少1个词汇，当前:",K.value.length),!1;if(0===f.value.length)return console.error("❌ gameBoard数据为空"),!1;const e=2*K.value.length;if(f.value.length!==e)return console.error(`❌ gameBoard数据不完整，期望${e}张卡片（${K.value.length}对词汇），实际:`,f.value.length),!1;for(let o=0;o<f.value.length;o++){const e=f.value[o];if(!e.position||!e.cardSize||!e.word)return console.error(`❌ 卡片${o+1}数据不完整:`,e),!1;if("number"!=typeof e.position.x||"number"!=typeof e.position.y)return console.error(`❌ 卡片${o+1}位置数据无效:`,e.position),!1}return u.value?(console.log("✅ 渲染准备状态检查通过"),!0):(console.error("❌ 当前关卡数据为空"),!1)},ve=async(e=1500)=>{console.log("🎮 开始模拟游戏加载进度...");const o=[{progress:0,message:"初始化游戏引擎..."},{progress:20,message:"加载游戏资源..."},{progress:40,message:"准备卡片数据..."},{progress:60,message:"生成游戏布局..."},{progress:80,message:"优化游戏性能..."},{progress:95,message:"最终检查..."},{progress:100,message:"加载完成！"}],l=e/o.length;for(let a=0;a<o.length;a++){const e=o[a];j.value=e.progress,B.value=e.message,console.log(`📊 加载进度: ${e.progress}% - ${e.message}`),await new Promise(e=>setTimeout(e,l))}console.log("✅ 游戏加载进度模拟完成")},pe=(e,o,l)=>{if(console.log("🎯 开始基于网格系统生成卡片位置..."),console.log("📊 输入参数检查:"),console.log("  - cards.length:",e.length),console.log("  - containerWidth:",o),console.log("  - containerHeight:",l),console.log("  - cards:",e),!Array.isArray(e)||0===e.length)return console.error("❌ 无效的卡片数组:",e),[];if(!o||!l)return console.error("❌ 无效的容器尺寸:",{containerWidth:o,containerHeight:l}),e;const a=new ge(o,l,e.length),t=e.map((e,t)=>{console.log(`🔄 为第${t+1}张卡片分配网格位置: ${e.type} - ${e.word?"english"===e.type?e.word.english:e.word.chinese:"unknown"}`);const n=a.getRandomAvailableGrid();if(!n)return console.error(`❌ 无法为卡片${t+1}分配网格位置`),{...e,position:{x:Math.round(o/2-80),y:Math.round(l/2-17.5)},cardSize:{width:160,height:35}};const r=a.getRandomPositionInGrid(n);return{...e,position:r,cardSize:{width:160,height:35},gridInfo:{gridIndex:n.gridIndex,row:n.row,col:n.col}}});console.log("✅ 网格位置分配完成"),console.log(`📊 使用了${a.occupiedGrids.size}个网格，总共${a.cols*a.rows}个可用网格`),console.log("🔍 验证返回数据:"),console.log("  - updatedCards.length:",t.length),console.log("  - 前3张卡片:",t.slice(0,3));const n=t.filter(e=>!(e.position&&"number"==typeof e.position.x&&"number"==typeof e.position.y&&e.cardSize&&e.word&&e.type));return n.length>0?console.error("❌ 发现无效卡片:",n):console.log("✅ 所有卡片数据验证通过"),t},we=(e,o,l)=>{let a=0,t=0;const n=30;console.log(`开始验证 ${e.length} 张卡片的布局质量...`);for(let r=0;r<e.length;r++){const s=e[r],i=s.position.x+s.cardSize.width,c=s.position.y+s.cardSize.height;(s.position.x<n||s.position.y<n||i>o-n||c>l-n)&&(t++,console.error(`❌ 卡片${r+1}(${s.type}:${s.word[s.type]})超出安全边界:`,{position:s.position,size:s.cardSize,cardRight:i,cardBottom:c,containerWidth:o,containerHeight:l,margin:n,"超出左边界":s.position.x<n,"超出上边界":s.position.y<n,"超出右边界":i>o-n,"超出下边界":c>l-n}));for(let o=r+1;o<e.length;o++){const l=e[o],t={x:s.position.x,y:s.position.y,width:s.cardSize.width,height:s.cardSize.height},n={x:l.position.x,y:l.position.y,width:l.cardSize.width,height:l.cardSize.height},i=ie(s.cardSize,l.cardSize);if(ue(t,n,i)){a++;const e=Math.sqrt(Math.pow(s.position.x-l.position.x,2)+Math.pow(s.position.y-l.position.y,2));console.error(`❌ 卡片${r+1}(${s.type})和卡片${o+1}(${l.type})间距不足:`,`实际距离: ${Math.round(e)}rpx < 要求间距: ${i}rpx`,`卡片1: (${s.position.x}, ${s.position.y}) 尺寸(${s.cardSize.width}×${s.cardSize.height})`,`卡片2: (${l.position.x}, ${l.position.y}) 尺寸(${l.cardSize.width}×${l.cardSize.height})`)}}}return console.log("📊 布局质量检查结果:"),console.log(`   - 总卡片数: ${e.length}`),console.log(`   - 超出边界: ${t}张`),console.log(`   - 距离过近: ${a}对`),console.log(`   - 容器尺寸: ${o}×${l}rpx`),0===a&&0===t?(console.log("✅ 卡片布局质量完美，无重叠和越界问题"),!0):(console.warn("⚠️ 卡片布局存在问题，需要优化算法"),!1)},fe=async()=>{console.log("🎲 开始异步初始化游戏棋盘..."),console.log("📊 棋盘初始化数据检查:"),console.log("  - currentLevel:",u.value),console.log("  - currentLevelDetail:",g.value),console.log("  - selectedLibraryInfo:",c.value),console.log("  - useMockData:",R.value);const e=K.value;if(console.log("  - wordsForCurrentLevel:",e),console.log("  - words.length:",e.length),e.length<1){const o=`词汇数量不足: 需要至少1个词汇，当前只有${e.length}个`;throw console.error("❌",o),new Error(o)}const{containerWidth:o,containerHeight:l}=de();console.log(`📐 游戏区域尺寸: ${o}rpx × ${l}rpx`);let a=[];console.log("🎨 预计算卡片尺寸和颜色...");const t={width:160,height:35},n=e.map(e=>({word:e,cardSize:t,color:C()}));console.log("📏 所有卡片使用固定尺寸:",t),console.log(`📍 开始创建卡片数据，共${n.length}对词汇...`);for(let r=0;r<n.length;r++){const{word:e,cardSize:o,color:l}=n[r];console.log(`🔄 创建第${r+1}/${n.length}对卡片数据: ${e.english} / ${e.chinese}`),a.push({id:T++,word:e,color:l,selected:!1,matched:!1,pairId:r,type:"english",cardSize:o}),a.push({id:T++,word:e,color:l,selected:!1,matched:!1,pairId:r,type:"chinese",cardSize:o}),r%2==1&&await new Promise(e=>setTimeout(e,10))}console.log("✅ 卡片数据创建完成，开始分配网格位置..."),a=pe(a,o,l),console.log("🔄 开始批量处理游戏数据..."),await(async(e,o,l)=>{console.log("🔄 开始批量处理游戏数据..."),console.log("📊 验证卡片布局质量..."),we(e,o,l),console.log("🔀 打乱卡片内容分配...");const a=e.filter(e=>"english"===e.type),t=e.filter(e=>"chinese"===e.type);for(let n=a.length-1;n>0;n--){const e=Math.floor(Math.random()*(n+1)),o=a[n].word,l=a[n].color,t=a[n].pairId;a[n].word=a[e].word,a[n].color=a[e].color,a[n].pairId=a[e].pairId,a[e].word=o,a[e].color=l,a[e].pairId=t}for(let n=t.length-1;n>0;n--){const e=Math.floor(Math.random()*(n+1)),o=t[n].word,l=t[n].color,a=t[n].pairId;t[n].word=t[e].word,t[n].color=t[e].color,t[n].pairId=t[e].pairId,t[e].word=o,t[e].color=l,t[e].pairId=a}return await new Promise(e=>setTimeout(e,10)),console.log("✅ 游戏数据批量处理完成"),console.log(`📊 英文卡片 (${a.length}张), 中文卡片 (${t.length}张)`),e})(a,o,l),console.log(`🎉 成功生成 ${a.length} 张卡片（${e.length}对词汇），位置分布完成`),console.log("🎯 所有计算完成，设置游戏状态..."),f.value=a,$.value=e.length,y.value=0,console.log(`✅ 游戏状态设置完成，总共${$.value}对词汇，准备渲染`)},me=()=>{const[e,o]=m.value;e.tile.type!==o.tile.type&&e.tile.pairId===o.tile.pairId?(e.tile.matched=!0,o.tile.matched=!0,e.tile.selected=!1,o.tile.selected=!1,y.value++,"english"===e.tile.type?e.tile.word.english:o.tile.word.english,"chinese"===e.tile.type?e.tile.word.chinese:o.tile.word.chinese,t.audioManager.playSoundEffect("success"),t.audioManager.vibrate("short"),console.log(`配对成功！已完成 ${y.value}/${$.value} 对`),y.value===$.value&&(console.log("🎉 恭喜！所有配对完成！"),Z(),b.value=((e,o)=>{if(null==o?void 0:o.timeLimit){const l=o.timeLimit;return e<=.5*l?3:e<=.75*l?2:1}return e<=30?3:e<=60?2:1})(L.value,g.value),console.log(`⭐ 游戏完成时间: ${L.value}秒, 获得星级: ${b.value}`),setTimeout(()=>{t.audioManager.playSoundEffect("complete"),t.audioManager.vibrate("long")},500),setTimeout(()=>{be(!0)},1e3))):(t.audioManager.playSoundEffect("fail"),l.showError("匹配错误，正在重置本关...",1500),setTimeout(async()=>{await Ie()},1500)),m.value=[],S.value=!1},ye=async()=>{var l,a;try{t.audioManager.playSoundEffect("click");const n=(null==(l=g.value)?void 0:l.id)||(null==(a=p.value)?void 0:a.id)||d.value;if(!n)return void console.warn("无法获取关卡ID，跳过收藏操作");z.value?(await o.weixinApi.removeFavorite(n),z.value=!1,e.index.showToast({title:"已取消收藏",icon:"success",duration:1500})):(await o.weixinApi.addFavorite(n),z.value=!0,e.index.showToast({title:"已添加收藏",icon:"success",duration:1500})),t.audioManager.playSoundEffect("complete")}catch(n){console.error("收藏操作失败:",n),t.audioManager.playSoundEffect("fail"),e.index.showToast({title:"操作失败，请重试",icon:"none",duration:1500})}},$e=async()=>{var e,l;try{const a=(null==(e=g.value)?void 0:e.id)||(null==(l=p.value)?void 0:l.id)||d.value;if(!a)return;const t=await o.weixinApi.getUserFavorites();z.value=t.favorites.some(e=>e.id===a)}catch(a){console.warn("检查收藏状态失败:",a),z.value=!1}},Se=()=>{e.index.navigateBack({delta:1})},xe=()=>{U.value=!1,console.log("关闭设置弹窗")},Me=e=>{N.value={...e},console.log("设置已更新:",e),e.backgroundMusic!==N.value.backgroundMusic&&(e.backgroundMusic?t.audioManager.playBackgroundMusic("game"):t.audioManager.stopBackgroundMusic())},ke=()=>{m.value=[],y.value=0,S.value=!1,E.value=!1,(()=>{const o=K.value;if(o.length<1)return void e.index.showToast({title:"词汇数量不足",icon:"none"});const{containerWidth:l,containerHeight:a}=de();console.log(`游戏区域尺寸: ${l}rpx × ${a}rpx`);let t=[];const n={width:160,height:35},r=o.map(e=>({word:e,cardSize:n,color:C()}));console.log("所有卡片使用固定尺寸:",n);for(let e=0;e<r.length;e++){const{word:o,cardSize:l,color:a}=r[e];t.push({id:T++,word:o,color:a,selected:!1,matched:!1,pairId:e,type:"english",cardSize:l}),t.push({id:T++,word:o,color:a,selected:!1,matched:!1,pairId:e,type:"chinese",cardSize:l})}console.log("卡片数据创建完成，开始分配网格位置...");const s=pe(t,l,a);console.log(s,"_cards");const i=s.filter(e=>"english"===e.type),c=s.filter(e=>"chinese"===e.type);for(let e=i.length-1;e>0;e--){const o=Math.floor(Math.random()*(e+1)),l=i[e].word,a=i[e].color,t=i[e].pairId;i[e].word=i[o].word,i[e].color=i[o].color,i[e].pairId=i[o].pairId,i[o].word=l,i[o].color=a,i[o].pairId=t}for(let e=c.length-1;e>0;e--){const o=Math.floor(Math.random()*(e+1)),l=c[e].word,a=c[e].color,t=c[e].pairId;c[e].word=c[o].word,c[e].color=c[o].color,c[e].pairId=c[o].pairId,c[o].word=l,c[o].color=a,c[o].pairId=t}t=[...i,...c],we(t,l,a),console.log("所有计算完成，设置游戏状态..."),f.value=t,$.value=o.length,y.value=0,console.log(`游戏状态设置完成，总共${$.value}对词汇`)})()},Ie=async()=>{try{if(console.log("🎮 开始重玩游戏..."),O.value=!0,P.value=!1,A.value=!1,_.value=!0,F.value=!1,G.value=!1,m.value=[],y.value=0,S.value=!1,E.value=!1,console.log("🧮 重新计算卡片位置..."),await new Promise(e=>setTimeout(e,100)),await fe(),console.log("🎨 准备重新渲染..."),_.value=!1,F.value=!0,await new Promise(e=>setTimeout(e,100)),!he())throw new Error("重玩数据生成失败");console.log("🎮 重新加载游戏..."),F.value=!1,G.value=!0,j.value=0,B.value="正在重新启动游戏...",await(async(e=1200)=>{console.log("🔄 开始模拟重玩加载进度...");const o=[{progress:0,message:"重置游戏状态..."},{progress:25,message:"清理旧数据..."},{progress:50,message:"重新生成布局..."},{progress:75,message:"优化卡片位置..."},{progress:100,message:"重玩准备完成！"}],l=e/o.length;for(let a=0;a<o.length;a++){const e=o[a];j.value=e.progress,B.value=e.message,console.log(`🔄 重玩进度: ${e.progress}% - ${e.message}`),await new Promise(e=>setTimeout(e,l))}console.log("✅ 重玩加载进度模拟完成")})(1200),console.log("🎉 重玩完成，开始渲染"),G.value=!1,O.value=!1,P.value=!0,console.log("⏱️ 重新开始游戏计时..."),V(),console.log("✨ 重玩成功，欢迎再次挑战！")}catch(e){console.error("❌ 重玩失败:",e),O.value=!1,A.value=!1,_.value=!1,F.value=!1,G.value=!1,P.value=!1,l.showError(`重玩失败: ${e.message||"未知错误"}`)}},Le=()=>{if(g.value)return console.log("🎯 从扩展关卡系统返回关卡选择页面"),void e.index.navigateBack({delta:1,success:()=>{l.showSuccess("请选择下一关卡",1e3)},fail:()=>{e.index.redirectTo({url:"/pages/level-selection/index"})}});Y.value?(d.value++,u.value={id:d.value,name:`第${d.value}关`,wordsCount:8},c.value&&((o,l)=>{const a=`currentLevel_${o}`;e.index.setStorageSync(a,l.toString())})(c.value.id,d.value),ke(),l.showSuccess(`进入第${d.value}关`,1e3)):l.showError("已经是最后一关了",1e3)},be=async e=>{D.value=e,e?(W.value="恭喜过关！",await He()):(W.value=H.value?"时间到期！":"挑战失败！",await ze()),E.value=!0},He=async()=>{try{v.value=!0;const l=e.index.getStorageSync("selectedLevel");if(l)try{const o=JSON.parse(l),a=`level_${o.id}_completed`;e.index.setStorageSync(a,"true"),console.log(`Level ${o.id} marked as completed locally`)}catch(o){console.error("Failed to save local level completion:",o)}await Te(),await Ce()}catch(l){console.error("处理游戏完成失败:",l)}finally{v.value=!1}},ze=async()=>{try{v.value=!0,console.log("游戏失败，记录尝试:",{finalTime:L.value,isTimeUp:H.value,stars:b.value})}catch(e){console.error("处理游戏失败失败:",e)}finally{v.value=!1}},Te=async()=>{try{if(!h.value||!h.value.id)return void console.warn("用户信息不存在，跳过通关接口调用");let a="";if(g.value?a=g.value.id:p.value?a=p.value.id:u.value&&(a=u.value.id.toString()),!a)return void console.warn("未找到关卡ID，跳过通关接口调用");console.log("调用通关接口:",{userId:h.value.id,levelId:a,completionTime:L.value,stars:b.value});const n=await o.weixinApi.completeLevel(o.weixinApi.getOpenid(),a,L.value);if(console.log("通关接口调用成功:",n),n.stars&&(b.value=n.stars,console.log(`⭐ 服务端返回星级: ${n.stars}`)),g.value){g.value.isCompleted=!0;const e=g.value;"userStars"in e&&(e.userStars=Math.max(e.userStars||0,b.value),e.bestTime=Math.min(e.bestTime||1/0,L.value))}n.hasUnlockedNewLevel?(t.audioManager.playSoundEffect("unlock"),e.index.showModal({title:"恭喜通关！",content:`${n.message}\n已解锁 ${n.unlockedLevels} 关！`,showCancel:!1,confirmText:"太棒了"})):!n.isVip&&n.remainingUnlocks<=0?e.index.showModal({title:"通关成功",content:`${n.message}\n今日解锁次数已用完，明天再来或分享获得额外机会！`,showCancel:!0,cancelText:"明天再来",confirmText:"立即分享",success:o=>{o.confirm&&e.index.showShareMenu({withShareTicket:!0})}}):l.showSuccess("恭喜通关！进度已同步",2e3),await Ce()}catch(a){console.error("调用通关接口失败:",a),l.showError("通关记录失败，但不影响游戏",2e3)}},Ce=async()=>{try{const l=await o.weixinApi.refreshUserInfo();l&&(h.value=l,e.index.setStorageSync("userInfo",JSON.stringify(l)),console.log("用户信息已刷新并保存到本地:",l))}catch(l){console.error("刷新用户信息失败:",l)}},Ee=o=>{try{const l=(new Date).toDateString(),a=`daily_share_reward_${o}_${l}`;e.index.setStorageSync(a,!0),console.log(`标记每日分享奖励完成 - 用户: ${o}, 日期: ${l}`)}catch(l){console.error("标记每日分享奖励失败:",l)}},We=async(l,a)=>{var t;try{if(!(null==(t=h.value)?void 0:t.id))return void console.warn("用户信息不存在，无法获取分享奖励");if(w)return void console.log("游戏分享奖励正在处理中，跳过重复请求");if((o=>{try{const l=(new Date).toDateString(),a=`daily_share_reward_${o}_${l}`,t=e.index.getStorageSync(a);return console.log(`检查每日分享奖励状态 - 用户: ${o}, 日期: ${l}, 已分享: ${!!t}`),!!t}catch(l){return console.error("检查每日分享奖励状态失败:",l),!1}})(h.value.id))return console.log("今日已获取过分享奖励，跳过本次请求"),void e.index.showToast({title:"今日已获得分享奖励",icon:"none",duration:2e3});w=!0,console.log("开始处理游戏页面分享奖励:",l),setTimeout(async()=>{try{const l=await o.weixinApi.getShareReward();l.success?(Ee(h.value.id),l.userInfo&&(h.value=l.userInfo,e.index.setStorageSync("userInfo",JSON.stringify(l.userInfo))),e.index.showModal({title:"分享奖励",content:`恭喜获得${l.reward.description}！可以继续挑战更多关卡了！今日分享奖励已领取完毕。`,showCancel:!1,confirmText:"太棒了",success:()=>{console.log("游戏分享奖励提示已显示")}}),console.log("游戏分享奖励获取成功:",l.reward)):(console.log("游戏分享奖励获取失败:",l.message),(l.message.includes("今日")||l.message.includes("已领取"))&&Ee(h.value.id))}catch(l){console.error("获取游戏分享奖励失败:",l)}finally{w=!1,console.log("游戏分享奖励处理完成，重置状态")}},2e3)}catch(n){console.error("处理游戏分享奖励失败:",n),w=!1}};return i({onShareAppMessage:async e=>{var o,l,t,n;console.log("游戏页面分享触发:",e);const r={page:"pages/game/index",levelId:(null==(o=g.value)?void 0:o.id)||(null==(l=p.value)?void 0:l.id),userId:null==(t=h.value)?void 0:t.id};return(null==(n=h.value)?void 0:n.id)&&We(e),await a.shareUtils.handleShareAppMessage(e,r)}}),(o,l)=>{var a;return e.e({a:g.value},g.value?e.e({b:e.t(g.value.name),c:e.t((null==(a=g.value.phrases)?void 0:a.length)||0),d:g.value.isCompleted},(g.value.isCompleted,{})):c.value?{f:e.t(c.value.name)}:{},{e:c.value,g:u.value},u.value?e.e({h:e.t(Q(I.value)),i:I.value<=10?1:"",j:v.value},(v.value,{}),{k:R.value},(R.value,{}),{l:e.t(O.value?"重玩中...":"重玩"),m:P.value?"":1,n:e.o(e=>P.value?Ie():null),o:A.value},A.value||_.value||F.value?{}:G.value?{s:e.t(O.value?"重新开始游戏...":"加载游戏中..."),t:e.t(B.value||(O.value?"正在为您重新准备游戏":"正在为您准备精彩的游戏体验")),v:j.value+"%",w:e.t(j.value)}:P.value||A.value||_.value||F.value||G.value?P.value?{A:e.f(f.value,(o,l,a)=>e.e({a:"english"===o.type},"english"===o.type?{b:e.t(o.word?o.word.english:"")}:{},{c:"chinese"===o.type},"chinese"===o.type?{d:e.t(o.word?o.word.chinese:"")}:{},{e:o.id,f:!o.matched,g:o.selected?1:"",h:o.matched?1:"",i:"english"===o.type?1:"",j:"chinese"===o.type?1:"",k:"english"===o.type&&o.word&&o.word.english.length<=4?1:"",l:"english"===o.type&&o.word&&o.word.english.length>4&&o.word.english.length<=7?1:"",m:"english"===o.type&&o.word&&o.word.english.length>7?1:"",n:o.color,o:o.position.x+"rpx",p:o.position.y+"rpx",q:o.cardSize.width+"rpx",r:o.cardSize.height+"rpx",s:e.o(e=>(e=>{if(S.value||E.value)return;t.audioManager.playSoundEffect("click");const o=f.value[e];if(!(o.matched||m.value.length>=2)){if(o.selected)return o.selected=!1,void(m.value=m.value.filter(o=>o.index!==e));o.selected=!0,m.value.push({index:e,tile:o}),2===m.value.length&&(S.value=!0,setTimeout(()=>{me()},500))}})(l),o.id)})),B:S.value?1:""}:{}:{y:e.o(le)},{p:_.value,q:F.value,r:G.value,x:!(P.value||A.value||_.value||F.value||G.value),z:P.value}):{},{C:E.value},E.value?e.e({D:e.t(W.value),E:D.value},D.value?{F:e.f(3,(e,o,l)=>({a:e,b:e<=b.value?1:""})),G:e.t(Q(L.value)),H:e.t(z.value?"💖":"🤍"),I:e.t(z.value?"已收藏":"收藏关卡"),J:z.value?1:"",K:e.o(ye)}:{},{L:D.value&&Y.value},D.value&&Y.value?{M:e.t(g.value?"选择关卡":"下一关"),N:e.o(Le)}:{},{O:e.o(Ie),P:e.o(Se)}):{},{Q:e.o(xe),R:e.o(Me),S:e.p({visible:U.value})})}}});i.__runtimeHooks=2;const c=e._export_sfc(i,[["__scopeId","data-v-dbde49c8"]]);wx.createPage(c);
